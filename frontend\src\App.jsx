/**
 * المكون الرئيسي للتطبيق
 * نظام مراقبة التهديدات السيبرانية العالمية
 */

import React, { useState } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// استيراد المكونات
import Header from './components/Header';
import Dashboard from './components/Dashboard';
import ThreatsList from './components/ThreatsList';
import Statistics from './components/Statistics';
import SystemStatus from './components/SystemStatus';
import AlertsPanel from './components/AlertsPanel';
import LoadingSpinner from './components/LoadingSpinner';
import ErrorBoundary from './components/ErrorBoundary';

// استيراد الأنماط
import './index.css';

// إنشاء عميل React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5 دقائق
      cacheTime: 10 * 60 * 1000, // 10 دقائق
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: 1,
    },
  },
});

function App() {
  // حالة التبويب النشط
  const [activeTab, setActiveTab] = useState('dashboard');
  
  // حالة الشريط الجانبي (للشاشات الصغيرة)
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // قائمة التبويبات
  const tabs = [
    {
      id: 'dashboard',
      name: 'لوحة المعلومات',
      icon: '📊',
      component: Dashboard
    },
    {
      id: 'threats',
      name: 'التهديدات',
      icon: '🛡️',
      component: ThreatsList
    },
    {
      id: 'statistics',
      name: 'الإحصائيات',
      icon: '📈',
      component: Statistics
    },
    {
      id: 'alerts',
      name: 'التنبيهات',
      icon: '🚨',
      component: AlertsPanel
    },
    {
      id: 'status',
      name: 'حالة النظام',
      icon: '⚙️',
      component: SystemStatus
    }
  ];

  // العثور على التبويب النشط
  const activeTabData = tabs.find(tab => tab.id === activeTab);
  const ActiveComponent = activeTabData?.component || Dashboard;

  return (
    <QueryClientProvider client={queryClient}>
      <ErrorBoundary>
        <div className="min-h-screen bg-gray-900 text-gray-50 font-arabic">
          {/* الرأس */}
          <Header 
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            sidebarOpen={sidebarOpen}
            setSidebarOpen={setSidebarOpen}
            tabs={tabs}
          />

          {/* المحتوى الرئيسي */}
          <main className="container mx-auto px-4 py-6">
            <div className="flex flex-col lg:flex-row gap-6">
              {/* الشريط الجانبي للتنقل (للشاشات الكبيرة) */}
              <aside className="hidden lg:block w-64 flex-shrink-0">
                <nav className="bg-gray-800 rounded-xl p-4 sticky top-6">
                  <h3 className="text-lg font-semibold mb-4 text-gray-200">
                    التنقل
                  </h3>
                  <ul className="space-y-2">
                    {tabs.map((tab) => (
                      <li key={tab.id}>
                        <button
                          onClick={() => setActiveTab(tab.id)}
                          className={`w-full text-right px-4 py-3 rounded-lg transition-all duration-200 flex items-center gap-3 ${
                            activeTab === tab.id
                              ? 'bg-blue-600 text-white shadow-lg'
                              : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                          }`}
                        >
                          <span className="text-xl">{tab.icon}</span>
                          <span className="font-medium">{tab.name}</span>
                        </button>
                      </li>
                    ))}
                  </ul>
                </nav>
              </aside>

              {/* المحتوى الرئيسي */}
              <div className="flex-1 min-w-0">
                <div className="bg-gray-800 rounded-xl p-6 shadow-xl">
                  {/* عنوان الصفحة */}
                  <div className="mb-6">
                    <h1 className="text-2xl font-bold text-gray-100 flex items-center gap-3">
                      <span className="text-3xl">{activeTabData?.icon}</span>
                      {activeTabData?.name}
                    </h1>
                    <p className="text-gray-400 mt-2">
                      {getTabDescription(activeTab)}
                    </p>
                  </div>

                  {/* محتوى التبويب */}
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <ActiveComponent />
                  </React.Suspense>
                </div>
              </div>
            </div>
          </main>

          {/* الشريط الجانبي للهواتف */}
          {sidebarOpen && (
            <div className="lg:hidden fixed inset-0 z-50 bg-black bg-opacity-50">
              <div className="fixed right-0 top-0 h-full w-64 bg-gray-800 p-4 shadow-xl">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-semibold text-gray-200">
                    التنقل
                  </h3>
                  <button
                    onClick={() => setSidebarOpen(false)}
                    className="text-gray-400 hover:text-white"
                  >
                    ✕
                  </button>
                </div>
                <ul className="space-y-2">
                  {tabs.map((tab) => (
                    <li key={tab.id}>
                      <button
                        onClick={() => {
                          setActiveTab(tab.id);
                          setSidebarOpen(false);
                        }}
                        className={`w-full text-right px-4 py-3 rounded-lg transition-all duration-200 flex items-center gap-3 ${
                          activeTab === tab.id
                            ? 'bg-blue-600 text-white shadow-lg'
                            : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                        }`}
                      >
                        <span className="text-xl">{tab.icon}</span>
                        <span className="font-medium">{tab.name}</span>
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}

          {/* Footer */}
          <footer className="bg-gray-800 border-t border-gray-700 py-4 mt-12">
            <div className="container mx-auto px-4 text-center text-gray-400">
              <p>
                نظام مراقبة التهديدات السيبرانية العالمية © 2024
              </p>
              <p className="text-sm mt-1">
                مطور بواسطة فريق الأمن السيبراني
              </p>
            </div>
          </footer>
        </div>
      </ErrorBoundary>


    </QueryClientProvider>
  );
}

/**
 * دالة مساعدة للحصول على وصف التبويب
 */
function getTabDescription(tabId) {
  const descriptions = {
    dashboard: 'نظرة شاملة على التهديدات والإحصائيات الحديثة',
    threats: 'قائمة مفصلة بجميع التهديدات السيبرانية المكتشفة',
    statistics: 'تحليل إحصائي متقدم للتهديدات والاتجاهات',
    alerts: 'إدارة التنبيهات والإشعارات الأمنية النشطة',
    status: 'حالة الخدمات والمصادر ومعلومات النظام'
  };

  return descriptions[tabId] || 'معلومات النظام';
}

export default App;
