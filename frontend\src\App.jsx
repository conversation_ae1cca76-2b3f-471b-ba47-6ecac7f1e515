/**
 * المكون الرئيسي للتطبيق
 * نظام مراقبة التهديدات السيبرانية العالمية
 */

import React, { useState } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// استيراد المكونات الاحترافية الجديدة
import ProfessionalHeader from './components/ProfessionalHeader';
import ProfessionalDashboard from './components/ProfessionalDashboard';
import ThreatsList from './components/ThreatsList';
import Statistics from './components/Statistics';
import SystemStatus from './components/SystemStatus';
import AlertsPanel from './components/AlertsPanel';
import ModernLoadingSpinner from './components/ModernLoadingSpinner';
import ErrorBoundary from './components/ErrorBoundary';

// استيراد الأنماط
import './index.css';

// إنشاء عميل React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5 دقائق
      cacheTime: 10 * 60 * 1000, // 10 دقائق
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: 1,
    },
  },
});

function App() {
  // حالة التبويب النشط
  const [activeTab, setActiveTab] = useState('dashboard');

  // قائمة التبويبات
  const tabs = [
    {
      id: 'dashboard',
      name: 'لوحة المعلومات',
      icon: '📊',
      component: ProfessionalDashboard
    },
    {
      id: 'threats',
      name: 'التهديدات',
      icon: '🛡️',
      component: ThreatsList
    },
    {
      id: 'statistics',
      name: 'الإحصائيات',
      icon: '📈',
      component: Statistics
    },
    {
      id: 'alerts',
      name: 'التنبيهات',
      icon: '🚨',
      component: AlertsPanel
    },
    {
      id: 'status',
      name: 'حالة النظام',
      icon: '⚙️',
      component: SystemStatus
    }
  ];

  // العثور على التبويب النشط
  const activeTabData = tabs.find(tab => tab.id === activeTab);
  const ActiveComponent = activeTabData?.component || ProfessionalDashboard;

  return (
    <QueryClientProvider client={queryClient}>
      <ErrorBoundary>
        <div className="min-h-screen bg-primary text-primary">
          {/* الرأس الاحترافي الجديد */}
          <ProfessionalHeader
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            tabs={tabs}
          />

          {/* المحتوى الرئيسي */}
          <main className="container mx-auto px-6 py-8">
            {/* محتوى التبويب */}
            <React.Suspense fallback={
              <div className="flex items-center justify-center min-h-96">
                <ModernLoadingSpinner message="جاري التحميل..." size="large" />
              </div>
            }>
              <ActiveComponent />
            </React.Suspense>
          </main>

          {/* Footer احترافي */}
          <footer className="bg-card-elevated border-t border-light mt-16">
            <div className="container mx-auto px-6 py-8">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {/* معلومات النظام */}
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-primary rounded-xl flex items-center justify-center">
                      <span className="text-xl">🛡️</span>
                    </div>
                    <h3 className="text-lg font-bold text-primary">مراقب التهديدات</h3>
                  </div>
                  <p className="text-muted text-sm leading-relaxed">
                    نظام مراقبة التهديدات السيبرانية العالمية المتقدم
                    يوفر حماية شاملة ومراقبة مستمرة للتهديدات الأمنية
                  </p>
                </div>

                {/* روابط سريعة */}
                <div className="space-y-4">
                  <h4 className="text-md font-semibold text-secondary">روابط سريعة</h4>
                  <div className="space-y-2">
                    <a href="#" className="block text-muted hover:text-secondary transition-colors text-sm">
                      دليل المستخدم
                    </a>
                    <a href="#" className="block text-muted hover:text-secondary transition-colors text-sm">
                      واجهة برمجة التطبيقات
                    </a>
                    <a href="#" className="block text-muted hover:text-secondary transition-colors text-sm">
                      الدعم التقني
                    </a>
                    <a href="#" className="block text-muted hover:text-secondary transition-colors text-sm">
                      تقارير الأمان
                    </a>
                  </div>
                </div>

                {/* معلومات الاتصال */}
                <div className="space-y-4">
                  <h4 className="text-md font-semibold text-secondary">معلومات النظام</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted">الإصدار:</span>
                      <span className="text-secondary font-medium">3.0.1</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted">آخر تحديث:</span>
                      <span className="text-secondary font-medium">اليوم</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted">وقت التشغيل:</span>
                      <span className="text-success font-medium">99.9%</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* خط الفصل */}
              <div className="border-t border-light/50 mt-8 pt-6">
                <div className="flex flex-col md:flex-row items-center justify-between gap-4">
                  <p className="text-muted text-sm">
                    © 2024 فريق الأمن السيبراني. جميع الحقوق محفوظة.
                  </p>
                  <div className="flex items-center gap-4 text-xs text-muted">
                    <span>سياسة الخصوصية</span>
                    <span>•</span>
                    <span>شروط الاستخدام</span>
                    <span>•</span>
                    <span>اتفاقية الترخيص</span>
                  </div>
                </div>
              </div>
            </div>
          </footer>
        </div>
      </ErrorBoundary>


    </QueryClientProvider>
  );
}



export default App;
