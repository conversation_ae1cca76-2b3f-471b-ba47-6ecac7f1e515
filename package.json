{"name": "cyber-threat-monitor", "version": "1.0.0", "description": "نظام مراقبة التهديدات السيبرانية العالمية", "main": "index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd backend && npm run dev", "client": "cd frontend && npm run dev", "build": "cd frontend && npm run build", "start": "cd backend && npm start", "install-all": "npm install && cd backend && npm install && cd ../frontend && npm install"}, "keywords": ["cybersecurity", "threat-monitoring", "arabic", "security"], "author": "Cyber Security Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "dependencies": {"@tailwindcss/postcss": "^4.1.8", "@tanstack/react-query-devtools": "^5.79.0"}}