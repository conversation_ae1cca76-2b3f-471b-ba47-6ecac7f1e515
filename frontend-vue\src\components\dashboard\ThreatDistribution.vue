<template>
  <div class="threat-distribution">
    <div class="distribution-header">
      <h3>توزيع التهديدات</h3>
      <p>حسب مستوى الخطورة</p>
    </div>
    
    <div class="chart-container">
      <div class="donut-chart">
        <svg viewBox="0 0 200 200" class="chart-svg">
          <circle 
            v-for="(segment, index) in chartSegments" 
            :key="index"
            cx="100" 
            cy="100" 
            r="80"
            fill="none"
            :stroke="segment.color"
            stroke-width="20"
            :stroke-dasharray="segment.dashArray"
            :stroke-dashoffset="segment.dashOffset"
            :transform="segment.transform"
            class="chart-segment"
          />
          
          <text x="100" y="95" text-anchor="middle" class="chart-total">
            {{ totalThreats }}
          </text>
          <text x="100" y="115" text-anchor="middle" class="chart-label">
            إجمالي التهديدات
          </text>
        </svg>
      </div>
      
      <div class="chart-legend">
        <div 
          v-for="item in distributionData" 
          :key="item.severity"
          class="legend-item"
        >
          <div class="legend-color" :style="{ backgroundColor: item.color }"></div>
          <div class="legend-content">
            <div class="legend-label">{{ item.severity }}</div>
            <div class="legend-value">{{ item.count }} ({{ item.percentage }}%)</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useThreatsStore } from '../../stores/threats'

export default {
  name: 'ThreatDistribution',
  setup() {
    const threatsStore = useThreatsStore()
    
    const distributionData = computed(() => {
      const threats = threatsStore.threats
      const total = threats.length
      
      const severities = [
        { severity: 'حرج', color: '#dc2626', count: 0 },
        { severity: 'عالي', color: '#d97706', count: 0 },
        { severity: 'متوسط', color: '#f97316', count: 0 },
        { severity: 'منخفض', color: '#059669', count: 0 }
      ]
      
      threats.forEach(threat => {
        const severity = severities.find(s => s.severity === threat.severity)
        if (severity) severity.count++
      })
      
      return severities.map(item => ({
        ...item,
        percentage: total > 0 ? Math.round((item.count / total) * 100) : 0
      }))
    })
    
    const totalThreats = computed(() => threatsStore.threats.length)
    
    const chartSegments = computed(() => {
      const total = totalThreats.value
      if (total === 0) return []
      
      const circumference = 2 * Math.PI * 80
      let currentOffset = 0
      
      return distributionData.value.map(item => {
        const percentage = item.count / total
        const dashLength = circumference * percentage
        const dashArray = `${dashLength} ${circumference - dashLength}`
        const dashOffset = -currentOffset
        
        currentOffset += dashLength
        
        return {
          color: item.color,
          dashArray,
          dashOffset,
          transform: 'rotate(-90 100 100)'
        }
      })
    })
    
    return {
      distributionData,
      totalThreats,
      chartSegments
    }
  }
}
</script>

<style lang="scss" scoped>
.threat-distribution {
  background: $gradient-bg-secondary;
  border: 1px solid $border-color-light;
  border-radius: $border-radius-2xl;
  padding: $spacing-xl;
  height: 100%;
}

.distribution-header {
  margin-bottom: $spacing-lg;
  
  h3 {
    font-size: $font-size-xl;
    font-weight: $font-weight-bold;
    color: $text-primary;
    margin-bottom: $spacing-xs;
  }
  
  p {
    font-size: $font-size-sm;
    color: $text-tertiary;
    margin: 0;
  }
}

.chart-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-lg;
}

.donut-chart {
  width: 200px;
  height: 200px;
}

.chart-svg {
  width: 100%;
  height: 100%;
}

.chart-segment {
  transition: all $transition-normal;
  
  &:hover {
    stroke-width: 25;
  }
}

.chart-total {
  font-size: 24px;
  font-weight: $font-weight-bold;
  fill: $text-primary;
  font-family: $font-family-mono;
}

.chart-label {
  font-size: 12px;
  fill: $text-muted;
}

.chart-legend {
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
  width: 100%;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  padding: $spacing-sm;
  background: rgba(10, 15, 28, 0.3);
  border-radius: $border-radius-lg;
  transition: all $transition-normal;
  
  &:hover {
    background: rgba(10, 15, 28, 0.5);
    transform: translateX(-2px);
  }
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.legend-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.legend-label {
  font-size: $font-size-sm;
  color: $text-secondary;
  font-weight: $font-weight-medium;
}

.legend-value {
  font-size: $font-size-sm;
  color: $text-primary;
  font-family: $font-family-mono;
  font-weight: $font-weight-bold;
}
</style>
