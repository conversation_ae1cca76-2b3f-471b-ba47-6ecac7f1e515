<template>
  <div class="system-health">
    <div class="health-header">
      <h3>صحة النظام</h3>
      <div class="health-status" :class="overallStatus">
        {{ getStatusText(overallStatus) }}
      </div>
    </div>
    
    <div class="health-metrics">
      <div class="metric-item">
        <div class="metric-label">استخدام المعالج</div>
        <div class="metric-bar">
          <div class="metric-fill" :style="{ width: cpuUsage + '%' }"></div>
        </div>
        <div class="metric-value">{{ cpuUsage }}%</div>
      </div>
      
      <div class="metric-item">
        <div class="metric-label">استخدام الذاكرة</div>
        <div class="metric-bar">
          <div class="metric-fill" :style="{ width: memoryUsage + '%' }"></div>
        </div>
        <div class="metric-value">{{ memoryUsage }}%</div>
      </div>
      
      <div class="metric-item">
        <div class="metric-label">مساحة القرص</div>
        <div class="metric-bar">
          <div class="metric-fill" :style="{ width: diskUsage + '%' }"></div>
        </div>
        <div class="metric-value">{{ diskUsage }}%</div>
      </div>
      
      <div class="metric-item">
        <div class="metric-label">استجابة الشبكة</div>
        <div class="metric-bar">
          <div class="metric-fill" :style="{ width: networkHealth + '%' }"></div>
        </div>
        <div class="metric-value">{{ networkLatency }}ms</div>
      </div>
    </div>
    
    <div class="health-summary">
      <div class="summary-item">
        <span class="summary-label">وقت التشغيل</span>
        <span class="summary-value">{{ uptime }}</span>
      </div>
      <div class="summary-item">
        <span class="summary-label">آخر إعادة تشغيل</span>
        <span class="summary-value">{{ lastRestart }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'

export default {
  name: 'SystemHealth',
  setup() {
    // البيانات التفاعلية
    const cpuUsage = ref(45)
    const memoryUsage = ref(62)
    const diskUsage = ref(38)
    const networkLatency = ref(85)
    const uptime = ref('15 يوم، 8 ساعات')
    const lastRestart = ref('15 ديسمبر 2024')
    
    // البيانات المحسوبة
    const networkHealth = computed(() => {
      if (networkLatency.value < 100) return 100
      if (networkLatency.value < 200) return 80
      if (networkLatency.value < 500) return 60
      return 40
    })
    
    const overallStatus = computed(() => {
      const metrics = [cpuUsage.value, memoryUsage.value, diskUsage.value]
      const avgUsage = metrics.reduce((sum, val) => sum + val, 0) / metrics.length
      
      if (avgUsage < 50 && networkHealth.value > 80) return 'excellent'
      if (avgUsage < 70 && networkHealth.value > 60) return 'good'
      if (avgUsage < 85) return 'warning'
      return 'critical'
    })
    
    // الوظائف
    const getStatusText = (status) => {
      const statusTexts = {
        excellent: 'ممتاز',
        good: 'جيد',
        warning: 'تحذير',
        critical: 'حرج'
      }
      return statusTexts[status] || 'غير معروف'
    }
    
    const updateMetrics = () => {
      // محاكاة تحديث المقاييس
      cpuUsage.value = Math.max(20, Math.min(90, cpuUsage.value + (Math.random() - 0.5) * 10))
      memoryUsage.value = Math.max(30, Math.min(95, memoryUsage.value + (Math.random() - 0.5) * 8))
      diskUsage.value = Math.max(20, Math.min(80, diskUsage.value + (Math.random() - 0.5) * 5))
      networkLatency.value = Math.max(50, Math.min(300, networkLatency.value + (Math.random() - 0.5) * 20))
    }
    
    // دورة الحياة
    let metricsInterval
    
    onMounted(() => {
      metricsInterval = setInterval(updateMetrics, 5000)
    })
    
    onUnmounted(() => {
      if (metricsInterval) {
        clearInterval(metricsInterval)
      }
    })
    
    return {
      cpuUsage,
      memoryUsage,
      diskUsage,
      networkLatency,
      networkHealth,
      uptime,
      lastRestart,
      overallStatus,
      getStatusText
    }
  }
}
</script>

<style lang="scss" scoped>
.system-health {
  background: $gradient-bg-secondary;
  border: 1px solid $border-color-light;
  border-radius: $border-radius-2xl;
  padding: $spacing-xl;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.health-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-lg;
  
  h3 {
    font-size: $font-size-xl;
    font-weight: $font-weight-bold;
    color: $text-primary;
  }
}

.health-status {
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-lg;
  font-size: $font-size-xs;
  font-weight: $font-weight-bold;
  
  &.excellent {
    background: rgba(5, 150, 105, 0.2);
    color: $color-success;
  }
  
  &.good {
    background: rgba(34, 211, 238, 0.2);
    color: $color-info;
  }
  
  &.warning {
    background: rgba(217, 119, 6, 0.2);
    color: $color-warning;
  }
  
  &.critical {
    background: rgba(220, 38, 38, 0.2);
    color: $color-error;
  }
}

.health-metrics {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
  margin-bottom: $spacing-lg;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: $spacing-md;
}

.metric-label {
  font-size: $font-size-sm;
  color: $text-secondary;
  min-width: 80px;
  flex-shrink: 0;
}

.metric-bar {
  flex: 1;
  height: 8px;
  background: rgba(10, 15, 28, 0.5);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.metric-fill {
  height: 100%;
  background: linear-gradient(90deg, $color-success 0%, $color-warning 70%, $color-error 100%);
  border-radius: 4px;
  transition: width $transition-normal;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
    animation: shimmer 2s infinite;
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.metric-value {
  font-size: $font-size-sm;
  color: $text-primary;
  font-family: $font-family-mono;
  font-weight: $font-weight-bold;
  min-width: 50px;
  text-align: left;
}

.health-summary {
  padding-top: $spacing-md;
  border-top: 1px solid $border-color;
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-label {
  font-size: $font-size-xs;
  color: $text-muted;
}

.summary-value {
  font-size: $font-size-xs;
  color: $text-secondary;
  font-family: $font-family-mono;
}
</style>
