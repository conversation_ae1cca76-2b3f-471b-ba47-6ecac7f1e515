<template>
  <footer class="status-bar">
    <div class="status-content">
      <div class="status-left">
        <div class="system-info">
          <span class="info-item">النظام: نشط</span>
          <span class="info-separator">•</span>
          <span class="info-item">المعالجة: {{ processingRate }}/ثانية</span>
          <span class="info-separator">•</span>
          <span class="info-item">الذاكرة: {{ memoryUsage }}%</span>
        </div>
      </div>
      
      <div class="status-center">
        <div class="connection-status">
          <div class="status-indicator" :class="connectionStatus"></div>
          <span class="status-text">{{ getConnectionText() }}</span>
        </div>
      </div>
      
      <div class="status-right">
        <div class="version-info">
          <span>الإصدار 3.2.1</span>
          <span class="info-separator">•</span>
          <span>© 2024 مركز الأمن السيبراني</span>
        </div>
      </div>
    </div>
  </footer>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'

export default {
  name: 'StatusBar',
  setup() {
    const processingRate = ref(45)
    const memoryUsage = ref(62)
    const connectionStatus = ref('connected')
    
    const getConnectionText = () => {
      const statusTexts = {
        connected: 'متصل بجميع المصادر',
        partial: 'اتصال جزئي',
        disconnected: 'غير متصل'
      }
      return statusTexts[connectionStatus.value] || 'حالة غير معروفة'
    }
    
    const updateStats = () => {
      processingRate.value = Math.floor(Math.random() * 20) + 35
      memoryUsage.value = Math.floor(Math.random() * 10) + 58
    }
    
    let statsInterval
    
    onMounted(() => {
      statsInterval = setInterval(updateStats, 3000)
    })
    
    onUnmounted(() => {
      if (statsInterval) {
        clearInterval(statsInterval)
      }
    })
    
    return {
      processingRate,
      memoryUsage,
      connectionStatus,
      getConnectionText
    }
  }
}
</script>

<style lang="scss" scoped>
.status-bar {
  height: 32px;
  background: rgba(10, 15, 28, 0.95);
  border-top: 1px solid $border-color;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.status-content {
  height: 100%;
  padding: 0 $spacing-lg;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: $font-size-xs;
  color: $text-muted;
}

.status-left,
.status-right {
  flex: 1;
}

.status-right {
  text-align: left;
}

.system-info,
.version-info {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
}

.info-separator {
  opacity: 0.5;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  
  &.connected {
    background: $color-success;
    animation: pulse 2s infinite;
  }
  
  &.partial {
    background: $color-warning;
  }
  
  &.disconnected {
    background: $color-error;
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
</style>
