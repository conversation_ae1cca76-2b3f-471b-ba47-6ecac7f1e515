/**
 * مكون مؤشر التحميل المحسن
 */

import React from 'react';

const ModernLoadingSpinner = ({ message = 'جاري التحميل...', size = 'medium', type = 'spinner' }) => {
  const sizeClasses = {
    small: 'w-6 h-6',
    medium: 'w-10 h-10',
    large: 'w-16 h-16'
  };

  const textSizes = {
    small: 'text-xs',
    medium: 'text-sm',
    large: 'text-base'
  };

  if (type === 'dots') {
    return (
      <div className="flex flex-col items-center justify-center p-8 animate-fadeIn">
        <div className="flex gap-2 mb-4">
          <div className="w-3 h-3 bg-gradient-primary rounded-full animate-bounce" style={{animationDelay: '0ms'}}></div>
          <div className="w-3 h-3 bg-gradient-primary rounded-full animate-bounce" style={{animationDelay: '150ms'}}></div>
          <div className="w-3 h-3 bg-gradient-primary rounded-full animate-bounce" style={{animationDelay: '300ms'}}></div>
        </div>
        <p className={`text-muted ${textSizes[size]} font-medium`}>{message}</p>
      </div>
    );
  }

  if (type === 'pulse') {
    return (
      <div className="flex flex-col items-center justify-center p-8 animate-fadeIn">
        <div className={`${sizeClasses[size]} bg-gradient-primary rounded-full animate-pulse mb-4 shadow-glow`}></div>
        <p className={`text-muted ${textSizes[size]} font-medium`}>{message}</p>
      </div>
    );
  }

  // النوع الافتراضي: spinner
  return (
    <div className="flex flex-col items-center justify-center p-8 animate-fadeIn">
      <div className="relative mb-4">
        {/* الدائرة الخارجية */}
        <div className={`${sizeClasses[size]} border-4 border-accent/20 rounded-full`}></div>
        {/* الدائرة المتحركة */}
        <div className={`${sizeClasses[size]} border-4 border-transparent border-t-info border-r-info rounded-full animate-spin absolute top-0 left-0`}></div>
        {/* النقطة المركزية */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-gradient-primary rounded-full animate-pulse"></div>
      </div>
      <p className={`text-muted ${textSizes[size]} font-medium animate-pulse`}>{message}</p>
    </div>
  );
};

/**
 * مكون تحميل الصفحة الكاملة
 */
export const FullPageLoader = ({ message = 'جاري تحميل النظام...' }) => {
  return (
    <div className="fixed inset-0 bg-primary/90 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-card rounded-2xl p-8 shadow-xl border max-w-sm w-full mx-4 animate-fadeIn">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-6 relative">
            <div className="w-16 h-16 border-4 border-accent/20 rounded-full"></div>
            <div className="w-16 h-16 border-4 border-transparent border-t-info border-r-info rounded-full animate-spin absolute top-0 left-0"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <span className="text-2xl animate-pulse">🛡️</span>
            </div>
          </div>
          <h3 className="text-lg font-semibold text-primary mb-2">مراقب التهديدات</h3>
          <p className="text-muted text-sm">{message}</p>
          <div className="mt-4 h-1 bg-accent/20 rounded-full overflow-hidden">
            <div className="h-full bg-gradient-primary animate-pulse rounded-full" style={{width: '60%'}}></div>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * مكون تحميل مضمن صغير
 */
export const InlineLoader = ({ size = 'small' }) => {
  const sizeClasses = {
    small: 'w-4 h-4',
    medium: 'w-5 h-5'
  };

  return (
    <div className={`${sizeClasses[size]} border-2 border-accent/20 border-t-info rounded-full animate-spin`}></div>
  );
};

export default ModernLoadingSpinner;
