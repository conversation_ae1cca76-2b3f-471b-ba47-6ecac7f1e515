// ===== نظام الألوان الاحترافي =====

// الألوان الأساسية - نظام داكن احترافي
$color-primary: #0f172a;
$color-secondary: #1e293b;
$color-tertiary: #334155;
$color-quaternary: #475569;

// ألوان التمييز - نظام احترافي
$color-accent-blue: #3b82f6;
$color-accent-cyan: #06b6d4;
$color-accent-purple: #8b5cf6;
$color-accent-emerald: #059669;

// ألوان الحالة - تدرج احترافي
$color-success: #059669;
$color-warning: #d97706;
$color-error: #dc2626;
$color-info: #0284c7;

// ألوان النص - تباين احترافي
$text-primary: #f1f5f9;
$text-secondary: #cbd5e1;
$text-tertiary: #94a3b8;
$text-quaternary: #64748b;
$text-muted: #475569;

// ألوان التهديدات - نظام متدرج
$threat-critical: #ef4444;
$threat-high: #f97316;
$threat-medium: #eab308;
$threat-low: #22c55e;
$threat-unknown: #6b7280;

// ===== نظام المسافات =====
$spacing-xs: 0.25rem;   // 4px
$spacing-sm: 0.5rem;    // 8px
$spacing-md: 1rem;      // 16px
$spacing-lg: 1.5rem;    // 24px
$spacing-xl: 2rem;      // 32px
$spacing-2xl: 3rem;     // 48px
$spacing-3xl: 4rem;     // 64px

// ===== نظام الخطوط =====
$font-family-primary: 'IBM Plex Sans Arabic', -apple-system, BlinkMacSystemFont, sans-serif;
$font-family-mono: 'JetBrains Mono', 'Fira Code', monospace;

// أحجام الخط
$font-size-xs: 0.75rem;    // 12px
$font-size-sm: 0.875rem;   // 14px
$font-size-base: 1rem;     // 16px
$font-size-lg: 1.125rem;   // 18px
$font-size-xl: 1.25rem;    // 20px
$font-size-2xl: 1.5rem;    // 24px
$font-size-3xl: 1.875rem;  // 30px
$font-size-4xl: 2.25rem;   // 36px
$font-size-5xl: 3rem;      // 48px

// أوزان الخط
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// ===== نظام الظلال =====
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
$shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
$shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

// ظلال ملونة
$shadow-blue: 0 10px 25px -5px rgba(79, 143, 247, 0.2);
$shadow-cyan: 0 10px 25px -5px rgba(34, 211, 238, 0.2);
$shadow-purple: 0 10px 25px -5px rgba(168, 85, 247, 0.2);
$shadow-emerald: 0 10px 25px -5px rgba(16, 185, 129, 0.2);

// ===== نظام الحدود =====
$border-width: 1px;
$border-radius-sm: 0.375rem;  // 6px
$border-radius-md: 0.5rem;    // 8px
$border-radius-lg: 0.75rem;   // 12px
$border-radius-xl: 1rem;      // 16px
$border-radius-2xl: 1.5rem;   // 24px
$border-radius-full: 50%;

// ألوان الحدود
$border-color: rgba(148, 163, 184, 0.1);
$border-color-light: rgba(148, 163, 184, 0.2);
$border-color-medium: rgba(148, 163, 184, 0.3);

// ===== نظام الانتقالات =====
$transition-fast: 150ms ease-in-out;
$transition-normal: 250ms ease-in-out;
$transition-slow: 350ms ease-in-out;

// منحنيات الانتقال
$ease-in-out-cubic: cubic-bezier(0.4, 0, 0.2, 1);
$ease-out-cubic: cubic-bezier(0, 0, 0.2, 1);
$ease-in-cubic: cubic-bezier(0.4, 0, 1, 1);

// ===== نقاط الكسر =====
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;
$breakpoint-2xl: 1536px;

// ===== متغيرات خاصة =====
$header-height: 4rem;
$sidebar-width: 16rem;
$sidebar-width-collapsed: 4rem;

// Z-index layers
$z-dropdown: 1000;
$z-sticky: 1020;
$z-fixed: 1030;
$z-modal-backdrop: 1040;
$z-modal: 1050;
$z-popover: 1060;
$z-tooltip: 1070;

// ===== تدرجات احترافية =====
$gradient-primary: linear-gradient(135deg, #{$color-accent-blue} 0%, #{$color-accent-purple} 100%);
$gradient-secondary: linear-gradient(135deg, #{$color-accent-cyan} 0%, #{$color-accent-emerald} 100%);
$gradient-danger: linear-gradient(135deg, #{$threat-critical} 0%, #{$threat-high} 100%);
$gradient-warning: linear-gradient(135deg, #{$threat-medium} 0%, #{$threat-high} 100%);
$gradient-success: linear-gradient(135deg, #{$color-success} 0%, #{$threat-low} 100%);

// تدرجات الخلفية
$gradient-bg-primary: linear-gradient(135deg, #{$color-primary} 0%, #{$color-secondary} 100%);
$gradient-bg-secondary: linear-gradient(145deg, #{$color-secondary} 0%, #{$color-tertiary} 100%);

// ===== متغيرات الشبكة =====
$container-max-width: 1400px;
$container-padding: $spacing-lg;

// Grid system
$grid-columns: 12;
$grid-gutter: $spacing-md;

// ===== متغيرات الأنيميشن =====
$animation-duration-fast: 200ms;
$animation-duration-normal: 300ms;
$animation-duration-slow: 500ms;

// ===== Mixins مساعدة =====
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@mixin truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin glass-effect {
  background: rgba(26, 35, 50, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid $border-color;
}

@mixin hover-lift {
  transition: transform $transition-normal, box-shadow $transition-normal;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: $shadow-lg;
  }
}

@mixin focus-ring {
  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(79, 143, 247, 0.3);
  }
}
