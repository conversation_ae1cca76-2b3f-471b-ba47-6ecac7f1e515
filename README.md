# نظام مراقبة التهديدات السيبرانية العالمية

نظام متكامل لمراقبة التهديدات السيبرانية العالمية باللغة العربية، مصمم للاستخدام الاحترافي في بيئات العمل الحقيقية.

## الميزات الرئيسية

- 🔍 **مراقبة شاملة**: جلب البيانات من مصادر متعددة (NVD, CISA, Saudi CERT, RSS feeds)
- 🌐 **دعم كامل للعربية**: واجهة مستخدم بالعربية مع دعم RTL
- 📊 **لوحة معلومات تفاعلية**: إحصائيات مباشرة وتحديث تلقائي
- 🎨 **تصميم عصري**: واجهة داكنة احترافية
- 🔄 **تحديث مستمر**: تحديث تلقائي كل 30 ثانية
- 📈 **تحليل متقدم**: تصنيف التهديدات حسب الخطورة والنوع
- 🔐 **أمان عالي**: حماية مفاتيح API ومعالجة شاملة للأخطاء

## البنية التقنية

### الواجهة الأمامية (Frontend)
- **React.js** مع **Vite** للأداء السريع
- **Tailwind CSS** للتصميم العصري
- **Axios** للتواصل مع API
- **React Query** لإدارة البيانات

### الخادم (Backend)
- **Node.js** مع **Express**
- **Axios** لجلب البيانات من المصادر الخارجية
- **Node-cron** للتحديث المجدول
- **CORS** للأمان
- **dotenv** لإدارة متغيرات البيئة

## التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
# تثبيت جميع التبعيات
npm run install-all
```

### 2. إعداد متغيرات البيئة
أنشئ ملف `.env` في مجلد `backend` وأضف:
```env
PORT=5000
NODE_ENV=development

# مفاتيح API الاختيارية (للميزات المتقدمة)
VIRUSTOTAL_API_KEY=your_key_here
ALIENVAULT_API_KEY=your_key_here
ABUSEIPDB_API_KEY=your_key_here
```

### 3. تشغيل النظام
```bash
# تشغيل النظام كاملاً (الخادم والواجهة)
npm run dev
```

أو تشغيل كل جزء منفصلاً:
```bash
# تشغيل الخادم فقط
npm run server

# تشغيل الواجهة فقط
npm run client
```

### 4. الوصول للنظام
- **الواجهة الأمامية**: http://localhost:5173
- **API الخادم**: http://localhost:5000

## حالة النظام الحالية ✅

النظام يعمل بنجاح ويجلب البيانات من جميع المصادر:

### المصادر المتصلة:
- ✅ **NVD (NIST)** - ثغرات أمنية
- ✅ **CISA** - ثغرات مستغلة
- ✅ **Saudi CERT** - تنبيهات أمنية
- ✅ **RSS Feeds** - أخبار الأمن السيبراني
  - The Hacker News
  - Krebs on Security
  - Bleeping Computer

### الإحصائيات الحالية:
- **إجمالي التهديدات**: 117+ تهديد
- **التحديث التلقائي**: كل 30 دقيقة
- **المصادر النشطة**: 6 مصادر
- **اللغة**: العربية مع دعم RTL كامل

## الميزات المكتملة ✨

### 🎯 الميزات الأساسية
- ✅ **لوحة معلومات تفاعلية** - نظرة شاملة على التهديدات
- ✅ **قائمة التهديدات مع فلترة متقدمة** - بحث وتصفية حسب الخطورة والمصدر
- ✅ **إحصائيات مفصلة** - تحليل البيانات والاتجاهات
- ✅ **مراقبة حالة النظام** - مراقبة الخدمات والأداء

### 🔄 التحديث والمزامنة
- ✅ **تحديث تلقائي كل 30 دقيقة**
- ✅ **تحديث يدوي فوري**
- ✅ **تخزين مؤقت ذكي** - تحسين الأداء
- ✅ **مراقبة حالة الاتصال**

### 🎨 واجهة المستخدم
- ✅ **تصميم عصري بخلفية داكنة**
- ✅ **دعم كامل للعربية مع RTL**
- ✅ **تصميم متجاوب** - يعمل على جميع الأجهزة
- ✅ **رسوم متحركة وتأثيرات بصرية**

### 🔍 البحث والفلترة
- ✅ **بحث نصي متقدم**
- ✅ **فلترة حسب الخطورة** (حرج، عالي، متوسط، منخفض)
- ✅ **فلترة حسب المصدر**
- ✅ **فلترة حسب الفئة**
- ✅ **ترتيب النتائج**

### 📊 الإحصائيات والتحليل
- ✅ **إحصائيات الخطورة** - توزيع التهديدات
- ✅ **إحصائيات المصادر** - أداء كل مصدر
- ✅ **إحصائيات زمنية** - آخر 24 ساعة و 7 أيام
- ✅ **معلومات CVE** - تتبع الثغرات المرقمة

### 🛡️ الأمان والموثوقية
- ✅ **معالجة شاملة للأخطاء**
- ✅ **تسجيل مفصل للأحداث**
- ✅ **حماية من تجاوز الحدود** (Rate Limiting)
- ✅ **تشفير الاتصالات**

## API Endpoints

| Method | Endpoint | الوصف |
|--------|----------|--------|
| GET | `/api/threats` | جلب جميع التهديدات مع إمكانية الفلترة |
| GET | `/api/stats` | الإحصائيات الشاملة |
| POST | `/api/threats/refresh` | تحديث البيانات يدوياً |
| GET | `/api/status` | حالة المصادر والنظام |

## مصادر البيانات

1. **NVD (NIST)** - قاعدة بيانات الثغرات الوطنية الأمريكية
2. **CISA** - الثغرات المعروفة المستغلة
3. **Saudi CERT** - تنبيهات الأمن السيبراني السعودي
4. **RSS Feeds** - أخبار الأمن السيبراني من مصادر موثوقة

## الدعم والمساهمة

هذا النظام مفتوح المصدر ومرحب بالمساهمات. لأي استفسارات أو مشاكل، يرجى فتح issue في المستودع.

## كيفية الاستخدام 📖

### 1. لوحة المعلومات الرئيسية
- عرض إحصائيات سريعة للتهديدات
- توزيع التهديدات حسب مستوى الخطورة
- أحدث التهديدات في آخر 24 ساعة
- أهم المصادر النشطة

### 2. قائمة التهديدات
- استعراض جميع التهديدات مع تفاصيل كاملة
- فلترة حسب الخطورة، المصدر، والفئة
- البحث النصي في العناوين والأوصاف
- عرض معلومات CVE و CVSS

### 3. الإحصائيات المتقدمة
- تحليل توزيع التهديدات
- إحصائيات المصادر والفئات
- معدلات النمو والاتجاهات
- مقاييس الأداء

### 4. مراقبة النظام
- حالة جميع المصادر والخدمات
- معلومات التخزين المؤقت
- إحصائيات الذاكرة والأداء
- أدوات الصيانة

## المتطلبات التقنية 🔧

### الحد الأدنى:
- **Node.js**: 16.0 أو أحدث
- **RAM**: 512 MB
- **مساحة القرص**: 100 MB
- **الشبكة**: اتصال إنترنت مستقر

### الموصى به:
- **Node.js**: 18.0 أو أحدث
- **RAM**: 1 GB أو أكثر
- **مساحة القرص**: 500 MB
- **المعالج**: متعدد النوى

## الأمان والخصوصية 🔒

- **لا يتم تخزين بيانات شخصية**
- **جميع البيانات من مصادر عامة**
- **التشفير في النقل** (HTTPS)
- **حماية من الهجمات الشائعة**
- **تسجيل آمن للأحداث**

## الترخيص

MIT License - انظر ملف LICENSE للتفاصيل.
