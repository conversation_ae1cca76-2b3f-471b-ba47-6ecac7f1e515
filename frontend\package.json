{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "devDependencies": {"@vitejs/plugin-react": "^4.5.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.4", "tailwindcss": "^3.4.17", "vite": "^6.3.5"}, "dependencies": {"@headlessui/react": "^2.2.4", "@tanstack/react-query": "^5.79.0", "axios": "^1.9.0", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-dom": "^19.1.0"}}