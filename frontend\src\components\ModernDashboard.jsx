/**
 * لوحة المعلومات الاحترافية الجديدة
 */

import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { threatsAPI } from '../services/api';
import { alertsAPI } from '../services/alertsApi';
import LoadingSpinner from './LoadingSpinner';
import ErrorMessage from './ErrorMessage';

const ModernDashboard = () => {
  // جلب البيانات
  const { data: threatsData, isLoading: threatsLoading, error: threatsError } = useQuery({
    queryKey: ['threats'],
    queryFn: threatsAPI.getAllThreats,
    refetchInterval: 30000
  });

  const { data: alertsData, isLoading: alertsLoading } = useQuery({
    queryKey: ['alerts'],
    queryFn: alertsAPI.getActiveAlerts,
    refetchInterval: 15000
  });

  const { data: statsData, isLoading: statsLoading } = useQuery({
    queryKey: ['stats'],
    queryFn: threatsAPI.getStatistics,
    refetchInterval: 60000
  });

  if (threatsLoading || alertsLoading || statsLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <LoadingSpinner message="جاري تحميل لوحة المعلومات..." size="large" />
      </div>
    );
  }

  if (threatsError) {
    return (
      <ErrorMessage 
        message="خطأ في تحميل البيانات"
        details={threatsError.message}
        type="error"
      />
    );
  }

  const threats = threatsData?.data || [];
  const alerts = alertsData?.data || [];
  const stats = statsData?.data || {};

  return (
    <div className="space-y-8 animate-fadeIn">
      {/* بطاقات الإحصائيات الرئيسية */}
      <section className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="إجمالي التهديدات"
          value={threats.length}
          icon="⚠️"
          color="info"
          trend="+12%"
          subtitle="آخر 24 ساعة"
        />
        <StatsCard
          title="التنبيهات النشطة"
          value={alerts.length}
          icon="🚨"
          color="critical"
          trend="+5"
          subtitle="تنبيهات جديدة"
        />
        <StatsCard
          title="التهديدات الحرجة"
          value={threats.filter(t => t.severity === 'حرج').length}
          icon="🔴"
          color="high"
          trend="-2%"
          subtitle="انخفاض طفيف"
        />
        <StatsCard
          title="المصادر النشطة"
          value={6}
          icon="🌐"
          color="low"
          trend="100%"
          subtitle="جميع المصادر متصلة"
        />
      </section>

      {/* التنبيهات العاجلة */}
      <section className="bg-card rounded-2xl p-6 border shadow-lg">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-primary flex items-center gap-3">
            <span className="text-3xl animate-pulse">🚨</span>
            التنبيهات العاجلة
          </h2>
          <button className="px-4 py-2 bg-gradient-primary text-white rounded-lg hover:scale-105 transition-all">
            عرض الكل
          </button>
        </div>

        {alerts.length > 0 ? (
          <div className="grid gap-4">
            {alerts.slice(0, 3).map((alert, index) => (
              <AlertCard key={alert.id} alert={alert} index={index} />
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <span className="text-6xl mb-4 block">✅</span>
            <h3 className="text-xl font-semibold text-secondary mb-2">لا توجد تنبيهات عاجلة</h3>
            <p className="text-muted">جميع التهديدات تحت السيطرة</p>
          </div>
        )}
      </section>

      {/* التهديدات الحديثة */}
      <section className="bg-card rounded-2xl p-6 border shadow-lg">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-primary flex items-center gap-3">
            <span className="text-3xl">📊</span>
            أحدث التهديدات
          </h2>
          <div className="flex gap-2">
            <FilterButton label="الكل" active />
            <FilterButton label="حرج" />
            <FilterButton label="عالي" />
            <FilterButton label="متوسط" />
          </div>
        </div>

        <div className="grid gap-4">
          {threats.slice(0, 5).map((threat, index) => (
            <ThreatCard key={threat.id} threat={threat} index={index} />
          ))}
        </div>
      </section>

      {/* الإحصائيات التفصيلية */}
      <section className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-card rounded-2xl p-6 border shadow-lg">
          <h3 className="text-xl font-bold text-primary mb-4 flex items-center gap-2">
            <span className="text-2xl">📈</span>
            توزيع التهديدات
          </h3>
          <ThreatDistribution threats={threats} />
        </div>

        <div className="bg-card rounded-2xl p-6 border shadow-lg">
          <h3 className="text-xl font-bold text-primary mb-4 flex items-center gap-2">
            <span className="text-2xl">🌍</span>
            المصادر النشطة
          </h3>
          <SourcesStatus />
        </div>
      </section>
    </div>
  );
};

/**
 * مكون بطاقة الإحصائيات
 */
const StatsCard = ({ title, value, icon, color, trend, subtitle }) => {
  const colorClasses = {
    info: 'bg-info/20 border-info/30 text-info',
    critical: 'bg-critical/20 border-critical/30 text-critical',
    high: 'bg-high/20 border-high/30 text-high',
    low: 'bg-low/20 border-low/30 text-low'
  };

  return (
    <div className={`${colorClasses[color]} rounded-2xl p-6 border backdrop-blur-sm hover:scale-105 transition-all animate-fadeIn`}>
      <div className="flex items-center justify-between mb-4">
        <span className="text-4xl animate-pulse">{icon}</span>
        <span className="text-sm font-medium bg-white/20 px-2 py-1 rounded-full">
          {trend}
        </span>
      </div>
      
      <div>
        <h3 className="text-sm font-medium text-secondary mb-1">{title}</h3>
        <p className="text-3xl font-bold mb-1">{value.toLocaleString('ar-SA')}</p>
        <p className="text-xs text-muted">{subtitle}</p>
      </div>
    </div>
  );
};

/**
 * مكون بطاقة التنبيه
 */
const AlertCard = ({ alert, index }) => {
  const priorityColors = {
    critical: 'border-r-critical bg-critical/10',
    high: 'border-r-high bg-high/10',
    medium: 'border-r-medium bg-medium/10',
    low: 'border-r-low bg-low/10'
  };

  return (
    <div 
      className={`${priorityColors[alert.priority]} border-r-4 bg-card p-4 rounded-lg hover:shadow-lg transition-all animate-slideIn`}
      style={{ animationDelay: `${index * 0.1}s` }}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h4 className="font-semibold text-secondary mb-1">{alert.message}</h4>
          <p className="text-sm text-muted mb-2">{alert.threat?.title}</p>
          <div className="flex items-center gap-4 text-xs text-muted">
            <span>المصدر: {alert.threat?.source}</span>
            <span>{new Date(alert.timestamp).toLocaleString('ar-SA')}</span>
          </div>
        </div>
        <button className="px-3 py-1 bg-gradient-primary text-white rounded-lg text-sm hover:scale-105 transition-all">
          عرض
        </button>
      </div>
    </div>
  );
};

/**
 * مكون بطاقة التهديد
 */
const ThreatCard = ({ threat, index }) => {
  const severityColors = {
    'حرج': 'text-critical',
    'عالي': 'text-high',
    'متوسط': 'text-medium',
    'منخفض': 'text-low'
  };

  return (
    <div 
      className="bg-secondary/50 p-4 rounded-lg border hover:shadow-lg transition-all animate-slideIn"
      style={{ animationDelay: `${index * 0.1}s` }}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h4 className="font-semibold text-secondary mb-1">{threat.title}</h4>
          <div className="flex items-center gap-4 text-sm">
            <span className={`font-medium ${severityColors[threat.severity]}`}>
              {threat.severity}
            </span>
            <span className="text-muted">المصدر: {threat.source}</span>
            <span className="text-muted">{new Date(threat.time).toLocaleDateString('ar-SA')}</span>
          </div>
        </div>
        <button className="px-3 py-1 bg-accent/20 hover:bg-accent/40 text-secondary rounded-lg text-sm transition-all">
          تفاصيل
        </button>
      </div>
    </div>
  );
};

/**
 * مكون زر الفلتر
 */
const FilterButton = ({ label, active = false }) => {
  return (
    <button className={`
      px-3 py-1 rounded-lg text-sm transition-all
      ${active 
        ? 'bg-gradient-primary text-white shadow-lg' 
        : 'bg-accent/20 text-secondary hover:bg-accent/40'
      }
    `}>
      {label}
    </button>
  );
};

/**
 * مكون توزيع التهديدات
 */
const ThreatDistribution = ({ threats }) => {
  const distribution = threats.reduce((acc, threat) => {
    acc[threat.severity] = (acc[threat.severity] || 0) + 1;
    return acc;
  }, {});

  return (
    <div className="space-y-3">
      {Object.entries(distribution).map(([severity, count]) => (
        <div key={severity} className="flex items-center justify-between">
          <span className="text-secondary">{severity}</span>
          <div className="flex items-center gap-2">
            <div className="w-24 h-2 bg-accent/20 rounded-full overflow-hidden">
              <div 
                className="h-full bg-gradient-primary rounded-full transition-all"
                style={{ width: `${(count / threats.length) * 100}%` }}
              ></div>
            </div>
            <span className="text-sm font-medium text-primary w-8">{count}</span>
          </div>
        </div>
      ))}
    </div>
  );
};

/**
 * مكون حالة المصادر
 */
const SourcesStatus = () => {
  const sources = [
    { name: 'NVD (NIST)', status: 'متصل', count: 50 },
    { name: 'CISA', status: 'متصل', count: 30 },
    { name: 'RSS Feeds', status: 'متصل', count: 37 },
    { name: 'Saudi CERT', status: 'غير متاح', count: 0 }
  ];

  return (
    <div className="space-y-3">
      {sources.map((source, index) => (
        <div key={source.name} className="flex items-center justify-between p-3 bg-secondary/30 rounded-lg">
          <div className="flex items-center gap-3">
            <div className={`w-3 h-3 rounded-full ${source.status === 'متصل' ? 'bg-low animate-pulse' : 'bg-critical'}`}></div>
            <span className="text-secondary font-medium">{source.name}</span>
          </div>
          <div className="text-left">
            <span className="text-sm text-primary font-bold">{source.count}</span>
            <span className="text-xs text-muted block">{source.status}</span>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ModernDashboard;
