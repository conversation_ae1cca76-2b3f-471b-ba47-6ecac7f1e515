/**
 * لوحة معلومات احترافية جديدة بالكامل
 */

import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { threatsAPI } from '../services/api';
import { alertsAPI } from '../services/alertsApi';

const ProfessionalDashboard = () => {
  // جلب البيانات
  const { data: threatsData, isLoading: threatsLoading, error: threatsError } = useQuery({
    queryKey: ['threats'],
    queryFn: threatsAPI.getAllThreats,
    refetchInterval: 30000
  });

  const { data: alertsData, isLoading: alertsLoading } = useQuery({
    queryKey: ['alerts'],
    queryFn: alertsAPI.getActiveAlerts,
    refetchInterval: 15000
  });

  if (threatsLoading || alertsLoading) {
    return <DashboardSkeleton />;
  }

  if (threatsError) {
    return <ErrorState error={threatsError} />;
  }

  const threats = threatsData?.data || [];
  const alerts = alertsData?.data || [];

  // حساب الإحصائيات
  const stats = {
    totalThreats: threats.length,
    criticalThreats: threats.filter(t => t.severity === 'حرج').length,
    highThreats: threats.filter(t => t.severity === 'عالي').length,
    activeAlerts: alerts.length,
    resolvedToday: 12,
    sourcesOnline: 6,
    totalSources: 6
  };

  return (
    <div className="space-y-8 animate-fadeIn">
      {/* بطاقات الإحصائيات الرئيسية */}
      <section>
        <h2 className="text-2xl font-bold text-primary mb-6 flex items-center gap-3">
          <span className="text-3xl">📊</span>
          نظرة عامة على النظام
        </h2>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          <MetricCard
            title="إجمالي التهديدات"
            value={stats.totalThreats}
            change="+12%"
            changeType="increase"
            icon="⚠️"
            color="info"
            subtitle="آخر 24 ساعة"
          />
          <MetricCard
            title="التهديدات الحرجة"
            value={stats.criticalThreats}
            change="-3"
            changeType="decrease"
            icon="🔴"
            color="error"
            subtitle="انخفاض عن الأمس"
          />
          <MetricCard
            title="التنبيهات النشطة"
            value={stats.activeAlerts}
            change="+5"
            changeType="increase"
            icon="🚨"
            color="warning"
            subtitle="تحتاج مراجعة"
          />
          <MetricCard
            title="المصادر المتصلة"
            value={`${stats.sourcesOnline}/${stats.totalSources}`}
            change="100%"
            changeType="stable"
            icon="🌐"
            color="success"
            subtitle="جميع المصادر نشطة"
          />
        </div>
      </section>

      {/* التنبيهات العاجلة */}
      <section>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-primary flex items-center gap-3">
            <span className="text-3xl animate-pulse">🚨</span>
            التنبيهات العاجلة
          </h2>
          <button className="px-4 py-2 bg-gradient-primary text-white rounded-xl hover:scale-105 transition-all duration-300 shadow-lg">
            عرض جميع التنبيهات
          </button>
        </div>

        <div className="bg-card-elevated rounded-2xl p-6 border">
          {alerts.length > 0 ? (
            <div className="space-y-4">
              {alerts.slice(0, 3).map((alert, index) => (
                <AlertCard key={alert.id} alert={alert} index={index} />
              ))}
            </div>
          ) : (
            <EmptyState 
              icon="✅"
              title="لا توجد تنبيهات عاجلة"
              description="جميع التهديدات تحت السيطرة"
            />
          )}
        </div>
      </section>

      {/* التهديدات الحديثة والتحليلات */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* التهديدات الحديثة */}
        <div className="lg:col-span-2">
          <h2 className="text-2xl font-bold text-primary mb-6 flex items-center gap-3">
            <span className="text-3xl">🎯</span>
            أحدث التهديدات
          </h2>
          
          <div className="bg-card-elevated rounded-2xl p-6 border">
            <div className="space-y-4">
              {threats.slice(0, 5).map((threat, index) => (
                <ThreatCard key={threat.id} threat={threat} index={index} />
              ))}
            </div>
          </div>
        </div>

        {/* التحليلات والإحصائيات */}
        <div className="space-y-6">
          {/* توزيع التهديدات */}
          <div className="bg-card-elevated rounded-2xl p-6 border">
            <h3 className="text-lg font-bold text-primary mb-4 flex items-center gap-2">
              <span className="text-xl">📈</span>
              توزيع التهديدات
            </h3>
            <ThreatDistribution threats={threats} />
          </div>

          {/* حالة المصادر */}
          <div className="bg-card-elevated rounded-2xl p-6 border">
            <h3 className="text-lg font-bold text-primary mb-4 flex items-center gap-2">
              <span className="text-xl">🔗</span>
              حالة المصادر
            </h3>
            <SourcesStatus />
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * مكون بطاقة المقياس
 */
const MetricCard = ({ title, value, change, changeType, icon, color, subtitle }) => {
  const colorClasses = {
    info: 'from-info/20 to-info/5 border-info/30',
    error: 'from-error/20 to-error/5 border-error/30',
    warning: 'from-warning/20 to-warning/5 border-warning/30',
    success: 'from-success/20 to-success/5 border-success/30'
  };

  const changeColors = {
    increase: 'text-error',
    decrease: 'text-success',
    stable: 'text-success'
  };

  return (
    <div className={`
      bg-gradient-to-br ${colorClasses[color]} 
      rounded-2xl p-6 border backdrop-blur-sm
      hover:scale-105 transition-all duration-300 cursor-pointer
      animate-fadeIn
    `}>
      <div className="flex items-center justify-between mb-4">
        <span className="text-4xl animate-pulse">{icon}</span>
        <span className={`text-sm font-semibold px-2 py-1 rounded-full bg-white/20 ${changeColors[changeType]}`}>
          {change}
        </span>
      </div>
      
      <div>
        <h3 className="text-sm font-medium text-secondary mb-2">{title}</h3>
        <p className="text-3xl font-bold text-primary mb-1">
          {typeof value === 'number' ? value.toLocaleString('ar-SA') : value}
        </p>
        <p className="text-xs text-muted">{subtitle}</p>
      </div>
    </div>
  );
};

/**
 * مكون بطاقة التنبيه
 */
const AlertCard = ({ alert, index }) => {
  const priorityStyles = {
    critical: 'border-r-error bg-error/5',
    high: 'border-r-warning bg-warning/5',
    medium: 'border-r-info bg-info/5',
    low: 'border-r-success bg-success/5'
  };

  return (
    <div 
      className={`
        ${priorityStyles[alert.priority]} border-r-4 bg-surface/30 p-4 rounded-lg 
        hover:shadow-lg transition-all duration-300 animate-slideInRight
      `}
      style={{ animationDelay: `${index * 100}ms` }}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h4 className="font-semibold text-secondary mb-2">{alert.message}</h4>
          <p className="text-sm text-muted mb-3">{alert.threat?.title}</p>
          <div className="flex items-center gap-4 text-xs text-muted">
            <span>المصدر: {alert.threat?.source}</span>
            <span>{new Date(alert.timestamp).toLocaleString('ar-SA')}</span>
          </div>
        </div>
        <button className="px-4 py-2 bg-gradient-primary text-white rounded-lg text-sm hover:scale-105 transition-all duration-300">
          عرض التفاصيل
        </button>
      </div>
    </div>
  );
};

/**
 * مكون بطاقة التهديد
 */
const ThreatCard = ({ threat, index }) => {
  const severityColors = {
    'حرج': 'text-error',
    'عالي': 'text-warning',
    'متوسط': 'text-info',
    'منخفض': 'text-success'
  };

  return (
    <div 
      className="bg-surface/30 p-4 rounded-lg border hover:shadow-lg transition-all duration-300 animate-slideInLeft"
      style={{ animationDelay: `${index * 100}ms` }}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h4 className="font-semibold text-secondary mb-2">{threat.title}</h4>
          <div className="flex items-center gap-4 text-sm">
            <span className={`font-medium ${severityColors[threat.severity]}`}>
              {threat.severity}
            </span>
            <span className="text-muted">المصدر: {threat.source}</span>
            <span className="text-muted">{new Date(threat.time).toLocaleDateString('ar-SA')}</span>
          </div>
        </div>
        <button className="px-3 py-1 bg-surface hover:bg-surface-light text-secondary rounded-lg text-sm transition-all duration-300">
          عرض
        </button>
      </div>
    </div>
  );
};

/**
 * مكون توزيع التهديدات
 */
const ThreatDistribution = ({ threats }) => {
  const distribution = threats.reduce((acc, threat) => {
    acc[threat.severity] = (acc[threat.severity] || 0) + 1;
    return acc;
  }, {});

  const total = threats.length;
  const severityOrder = ['حرج', 'عالي', 'متوسط', 'منخفض'];
  const colors = {
    'حرج': 'bg-error',
    'عالي': 'bg-warning',
    'متوسط': 'bg-info',
    'منخفض': 'bg-success'
  };

  return (
    <div className="space-y-4">
      {severityOrder.map((severity) => {
        const count = distribution[severity] || 0;
        const percentage = total > 0 ? (count / total) * 100 : 0;

        return (
          <div key={severity} className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-secondary font-medium">{severity}</span>
              <span className="text-primary font-bold">{count}</span>
            </div>
            <div className="h-2 bg-surface/50 rounded-full overflow-hidden">
              <div
                className={`h-full ${colors[severity]} rounded-full transition-all duration-1000 ease-out`}
                style={{ width: `${percentage}%` }}
              ></div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

/**
 * مكون حالة المصادر
 */
const SourcesStatus = () => {
  const sources = [
    { name: 'NVD (NIST)', status: 'متصل', count: 45, latency: '120ms' },
    { name: 'CISA Alerts', status: 'متصل', count: 23, latency: '85ms' },
    { name: 'RSS Feeds', status: 'متصل', count: 67, latency: '200ms' },
    { name: 'Saudi CERT', status: 'متصل', count: 12, latency: '95ms' },
    { name: 'CVE Database', status: 'متصل', count: 89, latency: '150ms' },
    { name: 'Threat Intel', status: 'صيانة', count: 0, latency: 'N/A' }
  ];

  return (
    <div className="space-y-3">
      {sources.map((source, index) => (
        <div
          key={source.name}
          className="flex items-center justify-between p-3 bg-surface/30 rounded-lg hover:bg-surface/50 transition-all duration-300"
        >
          <div className="flex items-center gap-3">
            <div className={`w-3 h-3 rounded-full ${
              source.status === 'متصل' ? 'bg-success animate-pulse' :
              source.status === 'صيانة' ? 'bg-warning' : 'bg-error'
            }`}></div>
            <div>
              <span className="text-secondary font-medium text-sm">{source.name}</span>
              <div className="text-xs text-muted">{source.latency}</div>
            </div>
          </div>
          <div className="text-right">
            <span className="text-primary font-bold">{source.count}</span>
            <div className="text-xs text-muted">{source.status}</div>
          </div>
        </div>
      ))}
    </div>
  );
};

/**
 * مكون حالة فارغة
 */
const EmptyState = ({ icon, title, description }) => {
  return (
    <div className="text-center py-12">
      <span className="text-6xl mb-4 block animate-bounce">{icon}</span>
      <h3 className="text-xl font-semibold text-secondary mb-2">{title}</h3>
      <p className="text-muted">{description}</p>
    </div>
  );
};

/**
 * مكون هيكل التحميل
 */
const DashboardSkeleton = () => {
  return (
    <div className="space-y-8 animate-pulse">
      {/* بطاقات الإحصائيات */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-surface/30 rounded-2xl p-6 border">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-surface rounded-full"></div>
              <div className="w-16 h-6 bg-surface rounded"></div>
            </div>
            <div className="space-y-2">
              <div className="w-24 h-4 bg-surface rounded"></div>
              <div className="w-16 h-8 bg-surface rounded"></div>
              <div className="w-20 h-3 bg-surface rounded"></div>
            </div>
          </div>
        ))}
      </div>

      {/* التنبيهات */}
      <div className="bg-surface/30 rounded-2xl p-6 border">
        <div className="w-48 h-6 bg-surface rounded mb-6"></div>
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-surface/50 p-4 rounded-lg">
              <div className="w-3/4 h-4 bg-surface rounded mb-2"></div>
              <div className="w-1/2 h-3 bg-surface rounded"></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

/**
 * مكون حالة الخطأ
 */
const ErrorState = ({ error }) => {
  return (
    <div className="text-center py-12">
      <span className="text-6xl mb-4 block">❌</span>
      <h3 className="text-xl font-semibold text-error mb-2">خطأ في تحميل البيانات</h3>
      <p className="text-muted mb-4">{error.message}</p>
      <button
        onClick={() => window.location.reload()}
        className="px-6 py-3 bg-gradient-primary text-white rounded-xl hover:scale-105 transition-all duration-300"
      >
        إعادة المحاولة
      </button>
    </div>
  );
};

export default ProfessionalDashboard;
