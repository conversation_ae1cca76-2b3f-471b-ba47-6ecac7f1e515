{"name": "@tanstack/react-query-devtools", "version": "5.79.0", "description": "Developer tools to interact with and visualize the TanStack/react-query cache", "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/query.git", "directory": "packages/react-query-devtools"}, "homepage": "https://tanstack.com/query", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "type": "module", "types": "build/legacy/index.d.ts", "main": "build/legacy/index.cjs", "module": "build/legacy/index.js", "exports": {".": {"@tanstack/custom-condition": "./src/index.ts", "import": {"types": "./build/modern/index.d.ts", "default": "./build/modern/index.js"}, "require": {"types": "./build/modern/index.d.cts", "default": "./build/modern/index.cjs"}}, "./production": {"import": {"types": "./build/modern/production.d.ts", "default": "./build/modern/production.js"}, "require": {"types": "./build/modern/production.d.cts", "default": "./build/modern/production.cjs"}}, "./build/modern/production.js": {"import": {"types": "./build/modern/production.d.ts", "default": "./build/modern/production.js"}, "require": {"types": "./build/modern/production.d.cts", "default": "./build/modern/production.cjs"}}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["build", "src", "!src/__tests__"], "dependencies": {"@tanstack/query-devtools": "5.76.0"}, "devDependencies": {"@testing-library/react": "^16.1.0", "@types/react": "^19.0.1", "@vitejs/plugin-react": "^4.3.4", "npm-run-all2": "^5.0.0", "react": "^19.0.0", "@tanstack/react-query": "5.79.0"}, "peerDependencies": {"react": "^18 || ^19", "@tanstack/react-query": "^5.79.0"}, "scripts": {}}