/**
 * مكون الرأس الاحترافي الجديد
 */

import React, { useState, useEffect } from 'react';

const ModernHeader = ({ activeTab, setActiveTab, tabs = [] }) => {
  const [isOnline, setIsOnline] = useState(true);
  const [notifications, setNotifications] = useState(3);
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      clearInterval(timer);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const formatTime = (date) => {
    return new Intl.DateTimeFormat('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).format(date);
  };

  return (
    <header className="bg-secondary backdrop-filter backdrop-blur-lg border-b border-info/20 shadow-xl sticky top-0 z-50 animate-fadeIn">
      <div className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          {/* الشعار والعنوان */}
          <div className="flex items-center gap-4">
            <div className="relative group">
              <div className="w-14 h-14 bg-gradient-primary rounded-2xl flex items-center justify-center shadow-glow hover:scale-105 transition-all cursor-pointer">
                <span className="text-3xl animate-pulse">🛡️</span>
              </div>
              {notifications > 0 && (
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-critical rounded-full flex items-center justify-center animate-bounce">
                  <span className="text-xs text-white font-bold">{notifications}</span>
                </div>
              )}
            </div>
            
            <div className="space-y-1">
              <h1 className="text-3xl font-extrabold text-primary animate-slideIn">
                <span className="bg-gradient-primary bg-clip-text text-transparent">
                  مراقب التهديدات السيبرانية
                </span>
              </h1>
              <p className="text-sm text-muted flex items-center gap-2 animate-fadeIn">
                <span className={`w-2 h-2 rounded-full animate-pulse ${isOnline ? 'bg-low' : 'bg-critical'}`}></span>
                نظام مراقبة التهديدات العالمية المتقدم
                <span className="text-xs bg-info/20 px-2 py-1 rounded-full">v2.0</span>
              </p>
            </div>
          </div>

          {/* شريط التنقل */}
          <nav className="hidden lg:flex items-center gap-2 bg-card/50 p-2 rounded-2xl backdrop-blur-sm">
            {tabs.map((tab) => (
              <NavButton
                key={tab.id}
                icon={tab.icon}
                label={tab.name}
                active={activeTab === tab.id}
                onClick={() => setActiveTab(tab.id)}
                badge={tab.id === 'alerts' ? notifications : null}
              />
            ))}
          </nav>

          {/* معلومات الحالة والتحكم */}
          <div className="flex items-center gap-4">
            {/* حالة الاتصال */}
            <div className="flex items-center gap-2 bg-card px-3 py-2 rounded-lg border hover:shadow-lg transition-all">
              <div className={`w-3 h-3 rounded-full ${isOnline ? 'bg-low animate-pulse' : 'bg-critical animate-bounce'}`}></div>
              <span className={`text-sm font-medium ${isOnline ? 'text-low' : 'text-critical'}`}>
                {isOnline ? 'متصل' : 'غير متصل'}
              </span>
            </div>

            {/* الوقت الحالي */}
            <div className="text-center bg-card px-4 py-2 rounded-xl border hover:shadow-glow transition-all">
              <p className="text-xs text-muted">الوقت الحالي</p>
              <p className="text-lg font-bold text-primary font-mono">
                {formatTime(currentTime)}
              </p>
            </div>

            {/* أزرار التحكم */}
            <div className="flex items-center gap-2">
              <ActionButton icon="🔄" tooltip="تحديث" onClick={() => window.location.reload()} />
              <ActionButton icon="⚙️" tooltip="الإعدادات" />
              <ActionButton icon="🔔" tooltip="الإشعارات" badge={notifications} />
              <ActionButton icon="👤" tooltip="الملف الشخصي" />
            </div>
          </div>
        </div>

        {/* شريط التقدم */}
        <div className="mt-4 h-1 bg-accent/20 rounded-full overflow-hidden">
          <div className="h-full bg-gradient-primary animate-pulse rounded-full transition-all duration-1000" 
               style={{width: `${Math.random() * 40 + 60}%`}}>
          </div>
        </div>

        {/* شريط التنقل المحمول */}
        <nav className="lg:hidden mt-4 flex items-center gap-1 overflow-x-auto pb-2">
          {tabs.map((tab) => (
            <MobileNavButton
              key={tab.id}
              icon={tab.icon}
              label={tab.name}
              active={activeTab === tab.id}
              onClick={() => setActiveTab(tab.id)}
              badge={tab.id === 'alerts' ? notifications : null}
            />
          ))}
        </nav>
      </div>
    </header>
  );
};

/**
 * مكون زر التنقل للشاشات الكبيرة
 */
const NavButton = ({ icon, label, active = false, badge = null, onClick }) => {
  return (
    <button 
      onClick={onClick}
      className={`
        relative flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 group
        ${active 
          ? 'bg-gradient-primary text-white shadow-glow scale-105' 
          : 'text-muted hover:text-secondary hover:bg-accent/20 hover:scale-105'
        }
      `}
    >
      <span className="text-xl group-hover:scale-110 transition-transform">{icon}</span>
      <span className="text-sm font-medium">{label}</span>
      
      {badge && (
        <div className="absolute -top-1 -right-1 w-5 h-5 bg-critical rounded-full flex items-center justify-center animate-bounce">
          <span className="text-xs text-white font-bold">{badge}</span>
        </div>
      )}
    </button>
  );
};

/**
 * مكون زر التنقل للشاشات الصغيرة
 */
const MobileNavButton = ({ icon, label, active = false, badge = null, onClick }) => {
  return (
    <button 
      onClick={onClick}
      className={`
        relative flex flex-col items-center gap-1 px-3 py-2 rounded-lg transition-all min-w-fit
        ${active 
          ? 'bg-gradient-primary text-white shadow-lg' 
          : 'text-muted hover:text-secondary hover:bg-accent/20'
        }
      `}
    >
      <span className="text-lg">{icon}</span>
      <span className="text-xs font-medium whitespace-nowrap">{label}</span>
      
      {badge && (
        <div className="absolute -top-1 -right-1 w-4 h-4 bg-critical rounded-full flex items-center justify-center">
          <span className="text-xs text-white font-bold">{badge}</span>
        </div>
      )}
    </button>
  );
};

/**
 * مكون زر الإجراء
 */
const ActionButton = ({ icon, tooltip, badge = null, onClick }) => {
  return (
    <button 
      onClick={onClick}
      title={tooltip}
      className="relative w-10 h-10 bg-accent/20 hover:bg-accent/40 rounded-xl flex items-center justify-center transition-all hover:scale-110 group"
    >
      <span className="text-lg group-hover:scale-110 transition-transform">{icon}</span>
      
      {badge && (
        <div className="absolute -top-1 -right-1 w-4 h-4 bg-critical rounded-full flex items-center justify-center">
          <span className="text-xs text-white font-bold">{badge}</span>
        </div>
      )}
    </button>
  );
};

export default ModernHeader;
