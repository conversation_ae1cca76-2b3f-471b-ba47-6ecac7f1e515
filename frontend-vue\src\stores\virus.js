import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useVirusStore = defineStore('virus', () => {
  // الحالة
  const scans = ref([])
  const isLoading = ref(false)
  const apiKey = ref('')
  
  // البيانات المحسوبة
  const totalScans = computed(() => scans.value.length)
  const threatsFound = computed(() => 
    scans.value.filter(scan => scan.malicious > 0).length
  )
  
  const recentScans = computed(() => 
    scans.value.slice(0, 10).sort((a, b) => new Date(b.date) - new Date(a.date))
  )
  
  // الإجراءات
  const setApiKey = (key) => {
    apiKey.value = key
    localStorage.setItem('virusTotal_apiKey', key)
  }
  
  const loadApiKey = () => {
    const savedKey = localStorage.getItem('virusTotal_apiKey')
    if (savedKey) {
      apiKey.value = savedKey
    }
  }
  
  const scanFile = async (file) => {
    if (!apiKey.value) {
      throw new Error('VirusTotal API key is required. Please configure it in settings.')
    }

    isLoading.value = true

    try {
      // رفع الملف إلى VirusTotal API v3
      const formData = new FormData()
      formData.append('file', file)

      const uploadResponse = await fetch('https://www.virustotal.com/api/v3/files', {
        method: 'POST',
        headers: {
          'x-apikey': apiKey.value
        },
        body: formData
      })

      if (!uploadResponse.ok) {
        const errorData = await uploadResponse.json()
        throw new Error(`Upload failed: ${errorData.error?.message || 'Unknown error'}`)
      }

      const uploadResult = await uploadResponse.json()
      const analysisId = uploadResult.data.id

      // انتظار تحليل الملف (polling)
      let analysisComplete = false
      let attempts = 0
      const maxAttempts = 30 // 5 دقائق بحد أقصى

      while (!analysisComplete && attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 10000)) // انتظار 10 ثواني

        const analysisResponse = await fetch(
          `https://www.virustotal.com/api/v3/analyses/${analysisId}`,
          {
            headers: {
              'x-apikey': apiKey.value
            }
          }
        )

        if (analysisResponse.ok) {
          const analysisData = await analysisResponse.json()
          if (analysisData.data.attributes.status === 'completed') {
            analysisComplete = true

            // جلب التقرير النهائي
            const fileId = analysisData.meta.file_info.sha256
            const reportResponse = await fetch(
              `https://www.virustotal.com/api/v3/files/${fileId}`,
              {
                headers: {
                  'x-apikey': apiKey.value
                }
              }
            )

            if (reportResponse.ok) {
              const report = await reportResponse.json()
              const stats = report.data.attributes.last_analysis_stats

              // إضافة النتيجة إلى القائمة
              const scanResult = {
                id: Date.now(),
                fileName: file.name,
                fileSize: file.size,
                date: new Date().toISOString(),
                malicious: stats.malicious || 0,
                total: Object.values(stats).reduce((a, b) => a + b, 0),
                permalink: `https://www.virustotal.com/gui/file/${fileId}`,
                scans: report.data.attributes.last_analysis_results || {},
                md5: report.data.attributes.md5,
                sha1: report.data.attributes.sha1,
                sha256: report.data.attributes.sha256,
                stats: stats
              }

              scans.value.unshift(scanResult)
              localStorage.setItem('virus_scans', JSON.stringify(scans.value))

              return scanResult
            }
          }
        }

        attempts++
      }

      if (!analysisComplete) {
        throw new Error('Analysis timeout. The file is still being processed.')
      }

    } catch (error) {
      console.error('File scan error:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }
  
  const scanUrl = async (url) => {
    if (!apiKey.value) {
      throw new Error('VirusTotal API key is required. Please configure it in settings.')
    }

    isLoading.value = true

    try {
      // فحص الرابط باستخدام VirusTotal API v3
      const formData = new FormData()
      formData.append('url', url)

      const scanResponse = await fetch('https://www.virustotal.com/api/v3/urls', {
        method: 'POST',
        headers: {
          'x-apikey': apiKey.value
        },
        body: formData
      })

      if (!scanResponse.ok) {
        const errorData = await scanResponse.json()
        throw new Error(`URL scan failed: ${errorData.error?.message || 'Unknown error'}`)
      }

      const scanResult = await scanResponse.json()
      const analysisId = scanResult.data.id

      // انتظار تحليل الرابط (polling)
      let analysisComplete = false
      let attempts = 0
      const maxAttempts = 20 // 3-4 دقائق بحد أقصى

      while (!analysisComplete && attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 10000)) // انتظار 10 ثواني

        const analysisResponse = await fetch(
          `https://www.virustotal.com/api/v3/analyses/${analysisId}`,
          {
            headers: {
              'x-apikey': apiKey.value
            }
          }
        )

        if (analysisResponse.ok) {
          const analysisData = await analysisResponse.json()
          if (analysisData.data.attributes.status === 'completed') {
            analysisComplete = true

            // جلب التقرير النهائي
            const urlId = btoa(url).replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '')
            const reportResponse = await fetch(
              `https://www.virustotal.com/api/v3/urls/${urlId}`,
              {
                headers: {
                  'x-apikey': apiKey.value
                }
              }
            )

            if (reportResponse.ok) {
              const report = await reportResponse.json()
              const stats = report.data.attributes.last_analysis_stats

              // إضافة النتيجة إلى القائمة
              const urlScanResult = {
                id: Date.now(),
                fileName: url,
                fileSize: 0,
                date: new Date().toISOString(),
                malicious: stats.malicious || 0,
                total: Object.values(stats).reduce((a, b) => a + b, 0),
                permalink: `https://www.virustotal.com/gui/url/${urlId}`,
                scans: report.data.attributes.last_analysis_results || {},
                type: 'url',
                stats: stats
              }

              scans.value.unshift(urlScanResult)
              localStorage.setItem('virus_scans', JSON.stringify(scans.value))

              return urlScanResult
            }
          }
        }

        attempts++
      }

      if (!analysisComplete) {
        throw new Error('Analysis timeout. The URL is still being processed.')
      }

    } catch (error) {
      console.error('URL scan error:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }
  
  const loadScans = () => {
    const savedScans = localStorage.getItem('virus_scans')
    if (savedScans) {
      scans.value = JSON.parse(savedScans)
    }
  }
  
  const clearScans = () => {
    scans.value = []
    localStorage.removeItem('virus_scans')
  }
  
  const deleteScan = (id) => {
    scans.value = scans.value.filter(scan => scan.id !== id)
    localStorage.setItem('virus_scans', JSON.stringify(scans.value))
  }
  
  const getScanById = (id) => {
    return scans.value.find(scan => scan.id === id)
  }
  
  // تحميل البيانات عند بدء التطبيق
  loadApiKey()
  loadScans()
  
  return {
    // الحالة
    scans,
    isLoading,
    apiKey,
    
    // البيانات المحسوبة
    totalScans,
    threatsFound,
    recentScans,
    
    // الإجراءات
    setApiKey,
    scanFile,
    scanUrl,
    clearScans,
    deleteScan,
    getScanById
  }
})
