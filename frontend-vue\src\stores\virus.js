import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useVirusStore = defineStore('virus', () => {
  // الحالة
  const scans = ref([])
  const isLoading = ref(false)
  const apiKey = ref('')
  
  // البيانات المحسوبة
  const totalScans = computed(() => scans.value.length)
  const threatsFound = computed(() => 
    scans.value.filter(scan => scan.malicious > 0).length
  )
  
  const recentScans = computed(() => 
    scans.value.slice(0, 10).sort((a, b) => new Date(b.date) - new Date(a.date))
  )
  
  // الإجراءات
  const setApiKey = (key) => {
    apiKey.value = key
    localStorage.setItem('virusTotal_apiKey', key)
  }
  
  const loadApiKey = () => {
    const savedKey = localStorage.getItem('virusTotal_apiKey')
    if (savedKey) {
      apiKey.value = savedKey
    }
  }
  
  const scanFile = async (file) => {
    if (!apiKey.value) {
      throw new Error('مطلوب مفتاح API من VirusTotal')
    }
    
    isLoading.value = true
    
    try {
      // رفع الملف إلى VirusTotal
      const formData = new FormData()
      formData.append('file', file)
      
      const uploadResponse = await fetch('https://www.virustotal.com/vtapi/v2/file/scan', {
        method: 'POST',
        headers: {
          'apikey': apiKey.value
        },
        body: formData
      })
      
      if (!uploadResponse.ok) {
        throw new Error('فشل في رفع الملف')
      }
      
      const uploadResult = await uploadResponse.json()
      
      // انتظار قليل ثم جلب النتائج
      await new Promise(resolve => setTimeout(resolve, 10000))
      
      const reportResponse = await fetch(
        `https://www.virustotal.com/vtapi/v2/file/report?apikey=${apiKey.value}&resource=${uploadResult.resource}`
      )
      
      if (!reportResponse.ok) {
        throw new Error('فشل في جلب التقرير')
      }
      
      const report = await reportResponse.json()
      
      // إضافة النتيجة إلى القائمة
      const scanResult = {
        id: Date.now(),
        fileName: file.name,
        fileSize: file.size,
        date: new Date().toISOString(),
        malicious: report.positives || 0,
        total: report.total || 0,
        permalink: report.permalink,
        scans: report.scans || {},
        md5: report.md5,
        sha1: report.sha1,
        sha256: report.sha256
      }
      
      scans.value.unshift(scanResult)
      
      // حفظ في localStorage
      localStorage.setItem('virus_scans', JSON.stringify(scans.value))
      
      return scanResult
      
    } catch (error) {
      console.error('خطأ في فحص الملف:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }
  
  const scanUrl = async (url) => {
    if (!apiKey.value) {
      throw new Error('مطلوب مفتاح API من VirusTotal')
    }
    
    isLoading.value = true
    
    try {
      // فحص الرابط
      const formData = new FormData()
      formData.append('url', url)
      
      const scanResponse = await fetch('https://www.virustotal.com/vtapi/v2/url/scan', {
        method: 'POST',
        headers: {
          'apikey': apiKey.value
        },
        body: formData
      })
      
      if (!scanResponse.ok) {
        throw new Error('فشل في فحص الرابط')
      }
      
      const scanResult = await scanResponse.json()
      
      // انتظار قليل ثم جلب النتائج
      await new Promise(resolve => setTimeout(resolve, 10000))
      
      const reportResponse = await fetch(
        `https://www.virustotal.com/vtapi/v2/url/report?apikey=${apiKey.value}&resource=${url}`
      )
      
      if (!reportResponse.ok) {
        throw new Error('فشل في جلب تقرير الرابط')
      }
      
      const report = await reportResponse.json()
      
      // إضافة النتيجة إلى القائمة
      const urlScanResult = {
        id: Date.now(),
        fileName: url,
        fileSize: 0,
        date: new Date().toISOString(),
        malicious: report.positives || 0,
        total: report.total || 0,
        permalink: report.permalink,
        scans: report.scans || {},
        type: 'url'
      }
      
      scans.value.unshift(urlScanResult)
      
      // حفظ في localStorage
      localStorage.setItem('virus_scans', JSON.stringify(scans.value))
      
      return urlScanResult
      
    } catch (error) {
      console.error('خطأ في فحص الرابط:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }
  
  const loadScans = () => {
    const savedScans = localStorage.getItem('virus_scans')
    if (savedScans) {
      scans.value = JSON.parse(savedScans)
    }
  }
  
  const clearScans = () => {
    scans.value = []
    localStorage.removeItem('virus_scans')
  }
  
  const deleteScan = (id) => {
    scans.value = scans.value.filter(scan => scan.id !== id)
    localStorage.setItem('virus_scans', JSON.stringify(scans.value))
  }
  
  const getScanById = (id) => {
    return scans.value.find(scan => scan.id === id)
  }
  
  // تحميل البيانات عند بدء التطبيق
  loadApiKey()
  loadScans()
  
  return {
    // الحالة
    scans,
    isLoading,
    apiKey,
    
    // البيانات المحسوبة
    totalScans,
    threatsFound,
    recentScans,
    
    // الإجراءات
    setApiKey,
    scanFile,
    scanUrl,
    clearScans,
    deleteScan,
    getScanById
  }
})
