/**
 * خدمة API للتواصل مع خادم التهديدات السيبرانية
 */

import axios from 'axios';

// إعداد الـ base URL للـ API
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// إنشاء مثيل axios مع الإعدادات الافتراضية
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 ثانية
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// إضافة interceptor للطلبات
apiClient.interceptors.request.use(
  (config) => {
    console.log(`🔄 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('❌ API Request Error:', error);
    return Promise.reject(error);
  }
);

// إضافة interceptor للاستجابات
apiClient.interceptors.response.use(
  (response) => {
    console.log(`✅ API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('❌ API Response Error:', error.response?.status, error.message);
    
    // معالجة أخطاء شائعة
    if (error.response?.status === 404) {
      console.warn('⚠️ المورد غير موجود');
    } else if (error.response?.status === 500) {
      console.error('🔥 خطأ في الخادم');
    } else if (error.code === 'ECONNABORTED') {
      console.error('⏰ انتهت مهلة الطلب');
    } else if (error.code === 'ERR_NETWORK') {
      console.error('🌐 خطأ في الشبكة');
    }
    
    return Promise.reject(error);
  }
);

/**
 * خدمة API للتهديدات
 */
export const threatsAPI = {
  /**
   * جلب جميع التهديدات مع إمكانية الفلترة
   */
  async getThreats(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      // إضافة الفلاتر إلى المعاملات
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value);
        }
      });

      const response = await apiClient.get(`/threats?${params.toString()}`);
      return response.data;
    } catch (error) {
      throw new Error(`خطأ في جلب التهديدات: ${error.message}`);
    }
  },

  /**
   * جلب تهديد محدد بالمعرف
   */
  async getThreatById(id) {
    try {
      const response = await apiClient.get(`/threats/${id}`);
      return response.data;
    } catch (error) {
      throw new Error(`خطأ في جلب تفاصيل التهديد: ${error.message}`);
    }
  },

  /**
   * البحث في التهديدات
   */
  async searchThreats(searchTerm, limit = 20) {
    try {
      const response = await apiClient.get(`/threats/search/${encodeURIComponent(searchTerm)}?limit=${limit}`);
      return response.data;
    } catch (error) {
      throw new Error(`خطأ في البحث: ${error.message}`);
    }
  },

  /**
   * جلب التهديدات حسب مستوى الخطورة
   */
  async getThreatsBySeverity(severity, limit = 50) {
    try {
      const response = await apiClient.get(`/threats/severity/${severity}?limit=${limit}`);
      return response.data;
    } catch (error) {
      throw new Error(`خطأ في جلب التهديدات حسب الخطورة: ${error.message}`);
    }
  },

  /**
   * جلب التهديدات الحديثة
   */
  async getRecentThreats(hours = 24, limit = 100) {
    try {
      const response = await apiClient.get(`/threats/recent/${hours}?limit=${limit}`);
      return response.data;
    } catch (error) {
      throw new Error(`خطأ في جلب التهديدات الحديثة: ${error.message}`);
    }
  },

  /**
   * تحديث البيانات يدوياً
   */
  async refreshThreats() {
    try {
      const response = await apiClient.post('/threats/refresh');
      return response.data;
    } catch (error) {
      throw new Error(`خطأ في تحديث البيانات: ${error.message}`);
    }
  },

  /**
   * تصدير التهديدات
   */
  async exportThreats(filters = {}, format = 'json') {
    try {
      const params = new URLSearchParams({ format, ...filters });
      const response = await apiClient.get(`/export/threats?${params.toString()}`);
      return response.data;
    } catch (error) {
      throw new Error(`خطأ في تصدير التهديدات: ${error.message}`);
    }
  }
};

/**
 * خدمة API للإحصائيات
 */
export const statsAPI = {
  /**
   * جلب الإحصائيات العامة
   */
  async getStatistics() {
    try {
      const response = await apiClient.get('/stats');
      return response.data;
    } catch (error) {
      throw new Error(`خطأ في جلب الإحصائيات: ${error.message}`);
    }
  }
};

/**
 * خدمة API لحالة النظام
 */
export const systemAPI = {
  /**
   * جلب حالة النظام والخدمات
   */
  async getSystemStatus() {
    try {
      const response = await apiClient.get('/status');
      return response.data;
    } catch (error) {
      throw new Error(`خطأ في جلب حالة النظام: ${error.message}`);
    }
  }
};

/**
 * دالة مساعدة للتحقق من اتصال الخادم
 */
export const checkServerConnection = async () => {
  try {
    const response = await apiClient.get('/', { timeout: 5000 });
    return { connected: true, data: response.data };
  } catch (error) {
    return { 
      connected: false, 
      error: error.message,
      isNetworkError: error.code === 'ERR_NETWORK' || error.code === 'ECONNABORTED'
    };
  }
};

/**
 * دالة مساعدة لمعالجة أخطاء API
 */
export const handleAPIError = (error) => {
  if (error.response) {
    // الخادم استجاب بخطأ
    const status = error.response.status;
    const message = error.response.data?.message || error.response.data?.error || error.message;
    
    switch (status) {
      case 400:
        return `طلب غير صالح: ${message}`;
      case 401:
        return 'غير مخول للوصول';
      case 403:
        return 'ممنوع الوصول';
      case 404:
        return 'المورد غير موجود';
      case 429:
        return 'تم تجاوز الحد المسموح من الطلبات';
      case 500:
        return `خطأ في الخادم: ${message}`;
      case 502:
        return 'خطأ في البوابة';
      case 503:
        return 'الخدمة غير متاحة مؤقتاً';
      default:
        return `خطأ غير متوقع (${status}): ${message}`;
    }
  } else if (error.request) {
    // لم يتم استلام استجابة
    if (error.code === 'ECONNABORTED') {
      return 'انتهت مهلة الاتصال، يرجى المحاولة مرة أخرى';
    } else if (error.code === 'ERR_NETWORK') {
      return 'خطأ في الشبكة، تحقق من اتصال الإنترنت';
    } else {
      return 'لا يمكن الوصول للخادم، تحقق من حالة الخادم';
    }
  } else {
    // خطأ في إعداد الطلب
    return `خطأ في الطلب: ${error.message}`;
  }
};

// تصدير العميل الافتراضي
export default apiClient;
