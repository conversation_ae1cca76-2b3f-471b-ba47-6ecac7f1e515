<template>
  <div class="virus-analyzer">
    <!-- قسم رفع الملفات -->
    <div class="upload-section">
      <div class="upload-card">
        <h2>فحص الملفات والروابط</h2>

        <!-- تبويبات -->
        <div class="tabs">
          <button
            :class="['tab', { active: activeTab === 'file' }]"
            @click="activeTab = 'file'"
          >
            📁 فحص ملف
          </button>
          <button
            :class="['tab', { active: activeTab === 'url' }]"
            @click="activeTab = 'url'"
          >
            🌐 فحص رابط
          </button>
          <button
            :class="['tab', { active: activeTab === 'settings' }]"
            @click="activeTab = 'settings'"
          >
            ⚙️ الإعدادات
          </button>
        </div>

        <!-- محتوى التبويبات -->
        <div class="tab-content">
          <!-- فحص الملفات -->
          <div v-if="activeTab === 'file'" class="file-upload">
            <div
              class="drop-zone"
              :class="{ 'drag-over': isDragOver }"
              @drop="handleDrop"
              @dragover.prevent="isDragOver = true"
              @dragleave="isDragOver = false"
              @click="$refs.fileInput.click()"
            >
              <div class="drop-icon">📤</div>
              <p class="drop-text">اسحب الملف هنا أو انقر للاختيار</p>
              <p class="drop-hint">الحد الأقصى: 32 ميجابايت</p>
            </div>

            <input
              ref="fileInput"
              type="file"
              @change="handleFileSelect"
              style="display: none"
            />

            <button
              v-if="selectedFile"
              class="scan-btn"
              @click="scanSelectedFile"
              :disabled="isLoading"
            >
              <span v-if="isLoading">🔄 جاري الفحص...</span>
              <span v-else>🔍 فحص الملف</span>
            </button>
          </div>

          <!-- فحص الروابط -->
          <div v-if="activeTab === 'url'" class="url-scan">
            <div class="input-group">
              <input
                v-model="urlToScan"
                type="url"
                placeholder="أدخل الرابط المراد فحصه"
                class="url-input"
              />
              <button
                class="scan-btn"
                @click="scanUrl"
                :disabled="!urlToScan || isLoading"
              >
                <span v-if="isLoading">🔄 جاري الفحص...</span>
                <span v-else>🔍 فحص الرابط</span>
              </button>
            </div>
          </div>

          <!-- الإعدادات -->
          <div v-if="activeTab === 'settings'" class="settings">
            <div class="setting-item">
              <label>مفتاح VirusTotal API:</label>
              <div class="input-group">
                <input
                  v-model="apiKeyInput"
                  type="password"
                  placeholder="أدخل مفتاح API"
                  class="api-input"
                />
                <button
                  class="save-btn"
                  @click="saveApiKey"
                >
                  💾 حفظ
                </button>
              </div>
              <p class="setting-hint">
                احصل على مفتاح API مجاني من
                <a href="https://www.virustotal.com/gui/join-us" target="_blank">VirusTotal</a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- نتائج الفحص -->
    <div v-if="recentScans.length > 0" class="results-section">
      <h2>نتائج الفحص الأخيرة</h2>

      <div class="scans-grid">
        <div
          v-for="scan in recentScans"
          :key="scan.id"
          class="scan-card"
          :class="getScanClass(scan)"
        >
          <div class="scan-header">
            <div class="scan-icon">
              {{ scan.type === 'url' ? '🌐' : '📄' }}
            </div>
            <div class="scan-info">
              <h3 class="scan-name">{{ scan.fileName }}</h3>
              <p class="scan-date">{{ formatDate(scan.date) }}</p>
            </div>
            <div class="scan-result">
              <span class="result-ratio">{{ scan.malicious }}/{{ scan.total }}</span>
            </div>
          </div>

          <div class="scan-details">
            <div class="detail-item">
              <span class="label">الحالة:</span>
              <span class="value" :class="getStatusClass(scan)">
                {{ getStatusText(scan) }}
              </span>
            </div>

            <div v-if="scan.fileSize" class="detail-item">
              <span class="label">الحجم:</span>
              <span class="value">{{ formatFileSize(scan.fileSize) }}</span>
            </div>
          </div>

          <div class="scan-actions">
            <button
              v-if="scan.permalink"
              class="action-btn"
              @click="openReport(scan.permalink)"
            >
              📊 التقرير الكامل
            </button>
            <button
              class="action-btn delete"
              @click="deleteScan(scan.id)"
            >
              🗑️ حذف
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- رسالة عدم وجود نتائج -->
    <div v-else class="empty-state">
      <div class="empty-icon">🔍</div>
      <h3>لا توجد فحوصات بعد</h3>
      <p>ابدأ بفحص ملف أو رابط لرؤية النتائج هنا</p>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useVirusStore } from '../stores/virus'

export default {
  name: 'Dashboard',
  setup() {
    const virusStore = useVirusStore()

    // البيانات التفاعلية
    const activeTab = ref('file')
    const selectedFile = ref(null)
    const urlToScan = ref('')
    const apiKeyInput = ref('')
    const isDragOver = ref(false)

    // البيانات المحسوبة
    const isLoading = computed(() => virusStore.isLoading)
    const recentScans = computed(() => virusStore.recentScans)

    // الوظائف
    const handleFileSelect = (event) => {
      const file = event.target.files[0]
      if (file) {
        if (file.size > 32 * 1024 * 1024) { // 32MB
          alert('حجم الملف كبير جداً. الحد الأقصى 32 ميجابايت')
          return
        }
        selectedFile.value = file
      }
    }

    const handleDrop = (event) => {
      event.preventDefault()
      isDragOver.value = false

      const files = event.dataTransfer.files
      if (files.length > 0) {
        const file = files[0]
        if (file.size > 32 * 1024 * 1024) {
          alert('حجم الملف كبير جداً. الحد الأقصى 32 ميجابايت')
          return
        }
        selectedFile.value = file
      }
    }

    const scanSelectedFile = async () => {
      if (!selectedFile.value) return

      try {
        await virusStore.scanFile(selectedFile.value)
        selectedFile.value = null
        // إعادة تعيين input
        const fileInput = document.querySelector('input[type="file"]')
        if (fileInput) fileInput.value = ''
      } catch (error) {
        alert('خطأ في فحص الملف: ' + error.message)
      }
    }

    const scanUrl = async () => {
      if (!urlToScan.value) return

      try {
        await virusStore.scanUrl(urlToScan.value)
        urlToScan.value = ''
      } catch (error) {
        alert('خطأ في فحص الرابط: ' + error.message)
      }
    }

    const saveApiKey = () => {
      if (apiKeyInput.value.trim()) {
        virusStore.setApiKey(apiKeyInput.value.trim())
        apiKeyInput.value = ''
        alert('تم حفظ مفتاح API بنجاح')
      }
    }

    const getScanClass = (scan) => {
      if (scan.malicious > 0) return 'malicious'
      return 'clean'
    }

    const getStatusClass = (scan) => {
      if (scan.malicious > 0) return 'danger'
      return 'success'
    }

    const getStatusText = (scan) => {
      if (scan.malicious > 0) {
        return `مصاب (${scan.malicious} محرك)`
      }
      return 'نظيف'
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleString('ar-SA')
    }

    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 بايت'
      const k = 1024
      const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const openReport = (permalink) => {
      window.open(permalink, '_blank')
    }

    const deleteScan = (id) => {
      if (confirm('هل تريد حذف هذا الفحص؟')) {
        virusStore.deleteScan(id)
      }
    }

    return {
      activeTab,
      selectedFile,
      urlToScan,
      apiKeyInput,
      isDragOver,
      isLoading,
      recentScans,
      handleFileSelect,
      handleDrop,
      scanSelectedFile,
      scanUrl,
      saveApiKey,
      getScanClass,
      getStatusClass,
      getStatusText,
      formatDate,
      formatFileSize,
      openReport,
      deleteScan
    }
  }
}
</script>

<style lang="scss" scoped>
.virus-analyzer {
  max-width: 1000px;
  margin: 0 auto;
}

// قسم الرفع
.upload-section {
  margin-bottom: $spacing-2xl;
}

.upload-card {
  background: rgba(26, 35, 50, 0.8);
  border: 1px solid rgba(79, 143, 247, 0.2);
  border-radius: $border-radius-2xl;
  padding: $spacing-2xl;
  backdrop-filter: blur(20px);

  h2 {
    color: $text-primary;
    font-size: $font-size-2xl;
    margin-bottom: $spacing-xl;
    text-align: center;
  }
}

// التبويبات
.tabs {
  display: flex;
  gap: $spacing-sm;
  margin-bottom: $spacing-xl;
  background: rgba(10, 15, 28, 0.5);
  padding: $spacing-sm;
  border-radius: $border-radius-xl;
}

.tab {
  flex: 1;
  padding: $spacing-md;
  background: transparent;
  border: none;
  color: $text-muted;
  border-radius: $border-radius-lg;
  cursor: pointer;
  transition: all $transition-normal;
  font-size: $font-size-sm;

  &.active {
    background: $color-accent-blue;
    color: white;
  }

  &:hover:not(.active) {
    background: rgba(255, 255, 255, 0.05);
    color: $text-secondary;
  }
}

// محتوى التبويبات
.tab-content {
  min-height: 200px;
}

// منطقة السحب والإفلات
.drop-zone {
  border: 2px dashed rgba(79, 143, 247, 0.3);
  border-radius: $border-radius-xl;
  padding: $spacing-2xl;
  text-align: center;
  cursor: pointer;
  transition: all $transition-normal;
  background: rgba(10, 15, 28, 0.3);

  &:hover, &.drag-over {
    border-color: $color-accent-blue;
    background: rgba(79, 143, 247, 0.1);
  }

  .drop-icon {
    font-size: 3rem;
    margin-bottom: $spacing-md;
  }

  .drop-text {
    font-size: $font-size-lg;
    color: $text-secondary;
    margin-bottom: $spacing-sm;
  }

  .drop-hint {
    font-size: $font-size-sm;
    color: $text-muted;
  }
}

// مجموعة الإدخال
.input-group {
  display: flex;
  gap: $spacing-md;
  align-items: center;
}

.url-input,
.api-input {
  flex: 1;
  padding: $spacing-md;
  background: rgba(10, 15, 28, 0.5);
  border: 1px solid rgba(79, 143, 247, 0.2);
  border-radius: $border-radius-lg;
  color: $text-primary;
  font-size: $font-size-sm;

  &:focus {
    outline: none;
    border-color: $color-accent-blue;
    box-shadow: 0 0 0 3px rgba(79, 143, 247, 0.1);
  }

  &::placeholder {
    color: $text-muted;
  }
}

// الأزرار
.scan-btn,
.save-btn {
  padding: $spacing-md $spacing-lg;
  background: $color-accent-blue;
  border: none;
  border-radius: $border-radius-lg;
  color: white;
  font-weight: $font-weight-medium;
  cursor: pointer;
  transition: all $transition-normal;

  &:hover:not(:disabled) {
    background: lighten($color-accent-blue, 10%);
    transform: translateY(-1px);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// الإعدادات
.settings {
  .setting-item {
    margin-bottom: $spacing-lg;

    label {
      display: block;
      color: $text-secondary;
      margin-bottom: $spacing-sm;
      font-weight: $font-weight-medium;
    }

    .setting-hint {
      margin-top: $spacing-sm;
      font-size: $font-size-xs;
      color: $text-muted;

      a {
        color: $color-accent-blue;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

// قسم النتائج
.results-section {
  h2 {
    color: $text-primary;
    font-size: $font-size-xl;
    margin-bottom: $spacing-lg;
  }
}

.scans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: $spacing-lg;
}

.scan-card {
  background: rgba(26, 35, 50, 0.8);
  border: 1px solid rgba(79, 143, 247, 0.2);
  border-radius: $border-radius-xl;
  padding: $spacing-lg;
  transition: all $transition-normal;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  }

  &.malicious {
    border-color: rgba(239, 68, 68, 0.5);
    background: rgba(239, 68, 68, 0.05);
  }

  &.clean {
    border-color: rgba(5, 150, 105, 0.5);
    background: rgba(5, 150, 105, 0.05);
  }
}

.scan-header {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  margin-bottom: $spacing-md;

  .scan-icon {
    font-size: 1.5rem;
  }

  .scan-info {
    flex: 1;
    min-width: 0;

    .scan-name {
      font-size: $font-size-sm;
      font-weight: $font-weight-medium;
      color: $text-primary;
      margin-bottom: 2px;
      @include truncate;
    }

    .scan-date {
      font-size: $font-size-xs;
      color: $text-muted;
      margin: 0;
    }
  }

  .scan-result {
    .result-ratio {
      font-size: $font-size-lg;
      font-weight: $font-weight-bold;
      font-family: $font-family-mono;
      color: $text-primary;
    }
  }
}

.scan-details {
  margin-bottom: $spacing-md;

  .detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: $spacing-xs;

    .label {
      font-size: $font-size-xs;
      color: $text-muted;
    }

    .value {
      font-size: $font-size-xs;
      font-weight: $font-weight-medium;

      &.success {
        color: $color-success;
      }

      &.danger {
        color: $color-error;
      }
    }
  }
}

.scan-actions {
  display: flex;
  gap: $spacing-sm;

  .action-btn {
    flex: 1;
    padding: $spacing-sm;
    background: rgba(79, 143, 247, 0.2);
    border: 1px solid $color-accent-blue;
    border-radius: $border-radius-lg;
    color: $color-accent-blue;
    font-size: $font-size-xs;
    cursor: pointer;
    transition: all $transition-normal;

    &:hover {
      background: $color-accent-blue;
      color: white;
    }

    &.delete {
      background: rgba(239, 68, 68, 0.2);
      border-color: $color-error;
      color: $color-error;

      &:hover {
        background: $color-error;
        color: white;
      }
    }
  }
}

// حالة فارغة
.empty-state {
  text-align: center;
  padding: $spacing-3xl;
  color: $text-muted;

  .empty-icon {
    font-size: 4rem;
    margin-bottom: $spacing-lg;
  }

  h3 {
    font-size: $font-size-xl;
    color: $text-secondary;
    margin-bottom: $spacing-sm;
  }

  p {
    font-size: $font-size-sm;
  }
}

// استجابة للشاشات
@media (max-width: $breakpoint-md) {
  .scans-grid {
    grid-template-columns: 1fr;
  }

  .upload-card {
    padding: $spacing-lg;
  }

  .tabs {
    flex-direction: column;
  }

  .input-group {
    flex-direction: column;
    align-items: stretch;
  }
}
</style>
