<template>
  <div class="analyzer-dashboard">
    <!-- قسم التحكم الرئيسي -->
    <div class="control-panel">
      <div class="panel-header">
        <h2 class="panel-title">Malware Analysis Center</h2>
        <p class="panel-description">Upload files or analyze URLs for comprehensive security scanning</p>
      </div>

      <!-- شريط التبويبات الاحترافي -->
      <div class="tab-navigation">
        <button
          :class="['nav-tab', { active: activeTab === 'file' }]"
          @click="activeTab = 'file'"
        >
          <svg class="tab-icon" viewBox="0 0 24 24" fill="none">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
            <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
          </svg>
          <span>File Analysis</span>
        </button>

        <button
          :class="['nav-tab', { active: activeTab === 'url' }]"
          @click="activeTab = 'url'"
        >
          <svg class="tab-icon" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <path d="M2 12h20" stroke="currentColor" stroke-width="2"/>
            <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" stroke="currentColor" stroke-width="2"/>
          </svg>
          <span>URL Analysis</span>
        </button>

        <button
          :class="['nav-tab', { active: activeTab === 'settings' }]"
          @click="activeTab = 'settings'"
        >
          <svg class="tab-icon" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
            <path d="M12 1v6m0 6v6" stroke="currentColor" stroke-width="2"/>
            <path d="m21 12-6 0m-6 0-6 0" stroke="currentColor" stroke-width="2"/>
          </svg>
          <span>Configuration</span>
        </button>
      </div>

      <!-- محتوى التبويبات -->
      <div class="tab-content">
        <!-- فحص الملفات -->
        <div v-if="activeTab === 'file'" class="analysis-section">
          <div
            class="upload-area"
            :class="{ 'drag-active': isDragOver, 'has-file': selectedFile }"
            @drop="handleDrop"
            @dragover.prevent="isDragOver = true"
            @dragleave="isDragOver = false"
            @click="$refs.fileInput.click()"
          >
            <div class="upload-content">
              <div class="upload-icon">
                <svg v-if="!selectedFile" viewBox="0 0 24 24" fill="none">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2"/>
                  <polyline points="17,8 12,3 7,8" stroke="currentColor" stroke-width="2"/>
                  <line x1="12" y1="3" x2="12" y2="15" stroke="currentColor" stroke-width="2"/>
                </svg>
                <svg v-else viewBox="0 0 24 24" fill="none">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                  <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                  <path d="M16 13H8" stroke="currentColor" stroke-width="2"/>
                  <path d="M16 17H8" stroke="currentColor" stroke-width="2"/>
                  <polyline points="10,9 9,9 8,9" stroke="currentColor" stroke-width="2"/>
                </svg>
              </div>

              <div class="upload-text">
                <h3 v-if="!selectedFile">Drop file here or click to browse</h3>
                <h3 v-else>{{ selectedFile.name }}</h3>
                <p v-if="!selectedFile">Supports all file types • Max size: 32MB</p>
                <p v-else>{{ formatFileSize(selectedFile.size) }} • Ready for analysis</p>
              </div>
            </div>

            <input
              ref="fileInput"
              type="file"
              @change="handleFileSelect"
              style="display: none"
            />
          </div>

          <div v-if="selectedFile" class="action-buttons">
            <button
              class="btn btn-primary"
              @click="scanSelectedFile"
              :disabled="isLoading"
            >
              <svg v-if="!isLoading" viewBox="0 0 24 24" fill="none">
                <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
              </svg>
              <svg v-else class="animate-spin" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" opacity="0.25"/>
                <path fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
              </svg>
              <span>{{ isLoading ? 'Analyzing...' : 'Start Analysis' }}</span>
            </button>

            <button
              class="btn btn-secondary"
              @click="clearFile"
            >
              <svg viewBox="0 0 24 24" fill="none">
                <path d="M3 6h18" stroke="currentColor" stroke-width="2"/>
                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" stroke="currentColor" stroke-width="2"/>
                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" stroke="currentColor" stroke-width="2"/>
              </svg>
              <span>Clear</span>
            </button>
          </div>
        </div>

        <!-- فحص الروابط -->
        <div v-if="activeTab === 'url'" class="analysis-section">
          <div class="url-input-container">
            <div class="input-wrapper">
              <svg class="input-icon" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                <path d="M2 12h20" stroke="currentColor" stroke-width="2"/>
                <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" stroke="currentColor" stroke-width="2"/>
              </svg>
              <input
                v-model="urlToScan"
                type="url"
                placeholder="Enter URL to analyze (e.g., https://example.com)"
                class="url-input"
              />
            </div>

            <button
              class="btn btn-primary"
              @click="scanUrl"
              :disabled="!urlToScan || isLoading"
            >
              <svg v-if="!isLoading" viewBox="0 0 24 24" fill="none">
                <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
              </svg>
              <svg v-else class="animate-spin" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" opacity="0.25"/>
                <path fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
              </svg>
              <span>{{ isLoading ? 'Analyzing...' : 'Analyze URL' }}</span>
            </button>
          </div>
        </div>

        <!-- الإعدادات -->
        <div v-if="activeTab === 'settings'" class="analysis-section">
          <div class="settings-form">
            <div class="form-group">
              <label class="form-label">
                <svg class="label-icon" viewBox="0 0 24 24" fill="none">
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                  <circle cx="12" cy="16" r="1" stroke="currentColor" stroke-width="2"/>
                  <path d="M7 11V7a5 5 0 0 1 10 0v4" stroke="currentColor" stroke-width="2"/>
                </svg>
                VirusTotal API Key
              </label>

              <div class="input-wrapper">
                <input
                  v-model="apiKeyInput"
                  type="password"
                  placeholder="Enter your VirusTotal API key"
                  class="form-input"
                />
                <button
                  class="btn btn-primary"
                  @click="saveApiKey"
                  :disabled="!apiKeyInput.trim()"
                >
                  <svg viewBox="0 0 24 24" fill="none">
                    <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z" stroke="currentColor" stroke-width="2"/>
                    <polyline points="17,21 17,13 7,13 7,21" stroke="currentColor" stroke-width="2"/>
                    <polyline points="7,3 7,8 15,8" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  <span>Save Configuration</span>
                </button>
              </div>

              <div class="form-help">
                <div class="help-content">
                  <h4>How to get your API key:</h4>
                  <ol>
                    <li>Visit <a href="https://www.virustotal.com/gui/join-us" target="_blank">VirusTotal Registration</a></li>
                    <li>Create a free account or sign in</li>
                    <li>Go to your profile settings</li>
                    <li>Copy your API key from the API Key section</li>
                    <li>Paste it in the field above and save</li>
                  </ol>
                  <p class="help-note">
                    <svg viewBox="0 0 24 24" fill="none">
                      <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                      <path d="M12 16v-4" stroke="currentColor" stroke-width="2"/>
                      <path d="M12 8h.01" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    Free accounts have rate limits. Consider upgrading for higher quotas.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- نتائج الفحص -->
    <div v-if="recentScans.length > 0" class="results-section">
      <div class="results-header">
        <h2 class="results-title">Analysis Results</h2>
        <p class="results-subtitle">Recent security scans and threat assessments</p>
      </div>

      <div class="results-grid">
        <div
          v-for="scan in recentScans"
          :key="scan.id"
          class="result-card"
          :class="getResultCardClass(scan)"
        >
          <div class="card-header">
            <div class="file-info">
              <div class="file-icon">
                <svg v-if="scan.type === 'url'" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                  <path d="M2 12h20" stroke="currentColor" stroke-width="2"/>
                  <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" stroke="currentColor" stroke-width="2"/>
                </svg>
                <svg v-else viewBox="0 0 24 24" fill="none">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                  <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                </svg>
              </div>
              <div class="file-details">
                <h3 class="file-name">{{ scan.fileName }}</h3>
                <p class="scan-time">{{ formatDate(scan.date) }}</p>
              </div>
            </div>

            <div class="threat-score">
              <div class="score-circle" :class="getScoreClass(scan)">
                <span class="score-text">{{ scan.malicious }}/{{ scan.total }}</span>
              </div>
            </div>
          </div>

          <div class="card-body">
            <div class="status-indicator">
              <div class="status-badge" :class="getStatusClass(scan)">
                <svg v-if="scan.malicious === 0" viewBox="0 0 24 24" fill="none">
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" stroke="currentColor" stroke-width="2"/>
                  <polyline points="22,4 12,14.01 9,11.01" stroke="currentColor" stroke-width="2"/>
                </svg>
                <svg v-else viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                  <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
                  <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>{{ getStatusText(scan) }}</span>
              </div>
            </div>

            <div v-if="scan.fileSize" class="file-metadata">
              <div class="metadata-item">
                <span class="metadata-label">File Size</span>
                <span class="metadata-value">{{ formatFileSize(scan.fileSize) }}</span>
              </div>
            </div>
          </div>

          <div class="card-actions">
            <button
              v-if="scan.permalink"
              class="btn btn-outline"
              @click="openReport(scan.permalink)"
            >
              <svg viewBox="0 0 24 24" fill="none">
                <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6" stroke="currentColor" stroke-width="2"/>
                <polyline points="15,3 21,3 21,9" stroke="currentColor" stroke-width="2"/>
                <line x1="10" y1="14" x2="21" y2="3" stroke="currentColor" stroke-width="2"/>
              </svg>
              <span>View Report</span>
            </button>

            <button
              class="btn btn-ghost"
              @click="deleteScan(scan.id)"
            >
              <svg viewBox="0 0 24 24" fill="none">
                <path d="M3 6h18" stroke="currentColor" stroke-width="2"/>
                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" stroke="currentColor" stroke-width="2"/>
                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" stroke="currentColor" stroke-width="2"/>
              </svg>
              <span>Delete</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- حالة فارغة احترافية -->
    <div v-else class="empty-state">
      <div class="empty-illustration">
        <svg viewBox="0 0 200 200" fill="none">
          <circle cx="100" cy="100" r="80" stroke="currentColor" stroke-width="2" opacity="0.1"/>
          <circle cx="100" cy="100" r="60" stroke="currentColor" stroke-width="2" opacity="0.2"/>
          <circle cx="100" cy="100" r="40" stroke="currentColor" stroke-width="2" opacity="0.3"/>
          <path d="M70 70L130 130M130 70L70 130" stroke="currentColor" stroke-width="3" opacity="0.4"/>
        </svg>
      </div>
      <div class="empty-content">
        <h3 class="empty-title">No Analysis Results Yet</h3>
        <p class="empty-description">
          Upload a file or enter a URL to start your first security analysis.
          Results will appear here with detailed threat assessments.
        </p>
        <button
          class="btn btn-primary"
          @click="activeTab = 'file'"
        >
          <svg viewBox="0 0 24 24" fill="none">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2"/>
            <polyline points="17,8 12,3 7,8" stroke="currentColor" stroke-width="2"/>
            <line x1="12" y1="3" x2="12" y2="15" stroke="currentColor" stroke-width="2"/>
          </svg>
          <span>Start Analysis</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useVirusStore } from '../stores/virus'

export default {
  name: 'Dashboard',
  setup() {
    const virusStore = useVirusStore()

    // البيانات التفاعلية
    const activeTab = ref('file')
    const selectedFile = ref(null)
    const urlToScan = ref('')
    const apiKeyInput = ref('')
    const isDragOver = ref(false)

    // البيانات المحسوبة
    const isLoading = computed(() => virusStore.isLoading)
    const recentScans = computed(() => virusStore.recentScans)

    // الوظائف
    const handleFileSelect = (event) => {
      const file = event.target.files[0]
      if (file) {
        if (file.size > 32 * 1024 * 1024) { // 32MB
          alert('حجم الملف كبير جداً. الحد الأقصى 32 ميجابايت')
          return
        }
        selectedFile.value = file
      }
    }

    const handleDrop = (event) => {
      event.preventDefault()
      isDragOver.value = false

      const files = event.dataTransfer.files
      if (files.length > 0) {
        const file = files[0]
        if (file.size > 32 * 1024 * 1024) {
          alert('حجم الملف كبير جداً. الحد الأقصى 32 ميجابايت')
          return
        }
        selectedFile.value = file
      }
    }

    const scanSelectedFile = async () => {
      if (!selectedFile.value) return

      try {
        await virusStore.scanFile(selectedFile.value)
        selectedFile.value = null
        // إعادة تعيين input
        const fileInput = document.querySelector('input[type="file"]')
        if (fileInput) fileInput.value = ''
      } catch (error) {
        alert('خطأ في فحص الملف: ' + error.message)
      }
    }

    const scanUrl = async () => {
      if (!urlToScan.value) return

      try {
        await virusStore.scanUrl(urlToScan.value)
        urlToScan.value = ''
      } catch (error) {
        alert('خطأ في فحص الرابط: ' + error.message)
      }
    }

    const saveApiKey = () => {
      if (apiKeyInput.value.trim()) {
        virusStore.setApiKey(apiKeyInput.value.trim())
        apiKeyInput.value = ''
        alert('تم حفظ مفتاح API بنجاح')
      }
    }

    const clearFile = () => {
      selectedFile.value = null
      const fileInput = document.querySelector('input[type="file"]')
      if (fileInput) fileInput.value = ''
    }

    const getResultCardClass = (scan) => {
      if (scan.malicious > 0) return 'threat-detected'
      return 'clean-file'
    }

    const getScoreClass = (scan) => {
      if (scan.malicious === 0) return 'score-clean'
      if (scan.malicious <= 3) return 'score-low'
      if (scan.malicious <= 10) return 'score-medium'
      return 'score-high'
    }

    const getStatusClass = (scan) => {
      if (scan.malicious > 0) return 'status-threat'
      return 'status-clean'
    }

    const getStatusText = (scan) => {
      if (scan.malicious === 0) return 'Clean'
      if (scan.malicious <= 3) return 'Low Risk'
      if (scan.malicious <= 10) return 'Medium Risk'
      return 'High Risk'
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleString('ar-SA')
    }

    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 بايت'
      const k = 1024
      const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const openReport = (permalink) => {
      window.open(permalink, '_blank')
    }

    const deleteScan = (id) => {
      if (confirm('هل تريد حذف هذا الفحص؟')) {
        virusStore.deleteScan(id)
      }
    }

    return {
      activeTab,
      selectedFile,
      urlToScan,
      apiKeyInput,
      isDragOver,
      isLoading,
      recentScans,
      handleFileSelect,
      handleDrop,
      scanSelectedFile,
      scanUrl,
      saveApiKey,
      clearFile,
      getResultCardClass,
      getScoreClass,
      getStatusClass,
      getStatusText,
      formatDate,
      formatFileSize,
      openReport,
      deleteScan
    }
  }
}
</script>

<style lang="scss" scoped>
.analyzer-dashboard {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

// Control Panel احترافي
.control-panel {
  background: rgba($color-secondary, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba($color-accent-blue, 0.1);
  border-radius: 24px;
  padding: 2.5rem;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba($color-accent-blue, 0.3), transparent);
  }
}

.panel-header {
  text-align: center;
  margin-bottom: 2.5rem;

  .panel-title {
    font-size: 2.25rem;
    font-weight: 700;
    color: $text-primary;
    margin-bottom: 0.75rem;
    letter-spacing: -0.025em;
  }

  .panel-description {
    font-size: 1.125rem;
    color: $text-tertiary;
    font-weight: 500;
    max-width: 600px;
    margin: 0 auto;
  }
}

// Navigation Tabs احترافية
.tab-navigation {
  display: flex;
  background: rgba($color-primary, 0.6);
  border-radius: 16px;
  padding: 0.5rem;
  margin-bottom: 2.5rem;
  gap: 0.5rem;
}

.nav-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: transparent;
  border: none;
  border-radius: 12px;
  color: $text-tertiary;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;

  .tab-icon {
    width: 20px;
    height: 20px;
    transition: transform 0.3s ease;
  }

  &:hover:not(.active) {
    color: $text-secondary;
    background: rgba($color-accent-blue, 0.05);

    .tab-icon {
      transform: scale(1.1);
    }
  }

  &.active {
    background: $color-accent-blue;
    color: white;
    box-shadow: 0 4px 12px rgba($color-accent-blue, 0.3);

    .tab-icon {
      transform: scale(1.05);
    }
  }
}

// Tab Content
.tab-content {
  min-height: 300px;
}

.analysis-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

// Upload Area احترافية
.upload-area {
  border: 2px dashed rgba($color-accent-blue, 0.2);
  border-radius: 20px;
  padding: 3rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba($color-primary, 0.3);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba($color-accent-blue, 0.05) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover, &.drag-active {
    border-color: $color-accent-blue;
    background: rgba($color-accent-blue, 0.05);
    transform: translateY(-2px);

    &::before {
      opacity: 1;
    }
  }

  &.has-file {
    border-color: $color-accent-emerald;
    background: rgba($color-accent-emerald, 0.05);
  }
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  position: relative;
  z-index: 2;
}

.upload-icon {
  width: 80px;
  height: 80px;
  color: $color-accent-blue;
  transition: all 0.3s ease;

  svg {
    width: 100%;
    height: 100%;
  }

  .upload-area:hover & {
    transform: scale(1.1);
    color: lighten($color-accent-blue, 10%);
  }

  .upload-area.has-file & {
    color: $color-accent-emerald;
  }
}

.upload-text {
  h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: $text-primary;
    margin-bottom: 0.5rem;
  }

  p {
    font-size: 1rem;
    color: $text-tertiary;
    margin: 0;
  }
}

// URL Input Container
.url-input-container {
  display: flex;
  gap: 1rem;
  align-items: flex-end;
}

.input-wrapper {
  flex: 1;
  position: relative;

  .input-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    color: $text-muted;
    z-index: 2;
  }
}

.url-input,
.form-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  background: rgba($color-primary, 0.5);
  border: 2px solid rgba($color-accent-blue, 0.2);
  border-radius: 16px;
  color: $text-primary;
  font-size: 1rem;
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: $color-accent-blue;
    box-shadow: 0 0 0 4px rgba($color-accent-blue, 0.1);
    background: rgba($color-primary, 0.7);
  }

  &::placeholder {
    color: $text-muted;
  }
}

// Buttons احترافية
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 16px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  white-space: nowrap;

  svg {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  &:hover:not(:disabled)::before {
    left: 100%;
  }
}

.btn-primary {
  background: linear-gradient(135deg, $color-accent-blue, $color-accent-purple);
  color: white;
  box-shadow: 0 4px 15px rgba($color-accent-blue, 0.3);

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba($color-accent-blue, 0.4);
  }
}

.btn-secondary {
  background: rgba($color-tertiary, 0.8);
  color: $text-secondary;
  border: 1px solid rgba($color-accent-blue, 0.2);

  &:hover:not(:disabled) {
    background: rgba($color-tertiary, 1);
    color: $text-primary;
    border-color: rgba($color-accent-blue, 0.4);
  }
}

.btn-outline {
  background: transparent;
  color: $color-accent-blue;
  border: 2px solid $color-accent-blue;

  &:hover:not(:disabled) {
    background: $color-accent-blue;
    color: white;
  }
}

.btn-ghost {
  background: transparent;
  color: $text-muted;
  border: 1px solid transparent;

  &:hover:not(:disabled) {
    background: rgba($color-error, 0.1);
    color: $color-error;
    border-color: rgba($color-error, 0.3);
  }
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

// Animation
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

// Settings Form احترافية
.settings-form {
  max-width: 600px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 2rem;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1rem;
  font-weight: 600;
  color: $text-secondary;
  margin-bottom: 1rem;

  .label-icon {
    width: 20px;
    height: 20px;
    color: $color-accent-blue;
  }
}

.form-help {
  margin-top: 1.5rem;
  padding: 1.5rem;
  background: rgba($color-primary, 0.5);
  border-radius: 16px;
  border: 1px solid rgba($color-accent-blue, 0.1);
}

.help-content {
  h4 {
    font-size: 1rem;
    font-weight: 600;
    color: $text-primary;
    margin-bottom: 1rem;
  }

  ol {
    margin: 0 0 1rem 1.5rem;
    padding: 0;
    color: $text-secondary;

    li {
      margin-bottom: 0.5rem;
      line-height: 1.5;
    }
  }

  a {
    color: $color-accent-blue;
    text-decoration: none;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }
}

.help-note {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  background: rgba($color-accent-blue, 0.05);
  border-radius: 12px;
  border-left: 4px solid $color-accent-blue;
  font-size: 0.875rem;
  color: $text-tertiary;
  margin: 0;

  svg {
    width: 16px;
    height: 16px;
    color: $color-accent-blue;
    flex-shrink: 0;
    margin-top: 0.125rem;
  }
}

// Results Section احترافية
.results-section {
  margin-top: 3rem;
}

.results-header {
  text-align: center;
  margin-bottom: 2.5rem;

  .results-title {
    font-size: 2rem;
    font-weight: 700;
    color: $text-primary;
    margin-bottom: 0.5rem;
  }

  .results-subtitle {
    font-size: 1.125rem;
    color: $text-tertiary;
    font-weight: 500;
  }
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
}

.result-card {
  background: rgba($color-secondary, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba($color-accent-blue, 0.1);
  border-radius: 20px;
  padding: 1.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba($color-accent-blue, 0.3), transparent);
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    border-color: rgba($color-accent-blue, 0.3);
  }

  &.threat-detected {
    border-color: rgba($color-error, 0.3);
    background: rgba($color-error, 0.02);

    &::before {
      background: linear-gradient(90deg, transparent, rgba($color-error, 0.3), transparent);
    }
  }

  &.clean-file {
    border-color: rgba($color-accent-emerald, 0.3);
    background: rgba($color-accent-emerald, 0.02);

    &::before {
      background: linear-gradient(90deg, transparent, rgba($color-accent-emerald, 0.3), transparent);
    }
  }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
  min-width: 0;
}

.file-icon {
  width: 48px;
  height: 48px;
  background: rgba($color-accent-blue, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: $color-accent-blue;
  flex-shrink: 0;

  svg {
    width: 24px;
    height: 24px;
  }
}

.file-details {
  flex: 1;
  min-width: 0;

  .file-name {
    font-size: 1rem;
    font-weight: 600;
    color: $text-primary;
    margin-bottom: 0.25rem;
    @include truncate;
  }

  .scan-time {
    font-size: 0.875rem;
    color: $text-muted;
    margin: 0;
  }
}

.threat-score {
  flex-shrink: 0;
}

.score-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.875rem;
  border: 3px solid;

  &.score-clean {
    background: rgba($color-accent-emerald, 0.1);
    border-color: $color-accent-emerald;
    color: $color-accent-emerald;
  }

  &.score-low {
    background: rgba($color-warning, 0.1);
    border-color: $color-warning;
    color: $color-warning;
  }

  &.score-medium {
    background: rgba(#f97316, 0.1);
    border-color: #f97316;
    color: #f97316;
  }

  &.score-high {
    background: rgba($color-error, 0.1);
    border-color: $color-error;
    color: $color-error;
  }
}

.card-body {
  margin-bottom: 1.5rem;
}

.status-indicator {
  margin-bottom: 1rem;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;

  svg {
    width: 16px;
    height: 16px;
  }

  &.status-clean {
    background: rgba($color-accent-emerald, 0.1);
    color: $color-accent-emerald;
    border: 1px solid rgba($color-accent-emerald, 0.3);
  }

  &.status-threat {
    background: rgba($color-error, 0.1);
    color: $color-error;
    border: 1px solid rgba($color-error, 0.3);
  }
}

.file-metadata {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.metadata-item {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .metadata-label {
    font-size: 0.875rem;
    color: $text-muted;
  }

  .metadata-value {
    font-size: 0.875rem;
    font-weight: 500;
    color: $text-secondary;
    font-family: 'SF Mono', 'Monaco', monospace;
  }
}

.card-actions {
  display: flex;
  gap: 0.75rem;
}

// Empty State احترافية
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  max-width: 500px;
  margin: 0 auto;
}

.empty-illustration {
  width: 120px;
  height: 120px;
  margin: 0 auto 2rem;
  color: $text-muted;
  opacity: 0.5;

  svg {
    width: 100%;
    height: 100%;
  }
}

.empty-content {
  .empty-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: $text-secondary;
    margin-bottom: 1rem;
  }

  .empty-description {
    font-size: 1rem;
    color: $text-muted;
    line-height: 1.6;
    margin-bottom: 2rem;
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .results-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }

  .url-input-container {
    flex-direction: column;
    align-items: stretch;
  }
}

@media (max-width: 768px) {
  .control-panel {
    padding: 2rem;
  }

  .panel-header .panel-title {
    font-size: 2rem;
  }

  .tab-navigation {
    flex-direction: column;
    gap: 0.5rem;
  }

  .results-grid {
    grid-template-columns: 1fr;
  }

  .card-actions {
    flex-direction: column;
  }

  .action-buttons {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .analyzer-dashboard {
    gap: 2rem;
  }

  .upload-area {
    padding: 2rem;
  }

  .upload-icon {
    width: 60px;
    height: 60px;
  }

  .upload-text h3 {
    font-size: 1.25rem;
  }
}
</style>
