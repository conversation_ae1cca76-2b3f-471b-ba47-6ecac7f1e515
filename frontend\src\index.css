/* ===== نظام التصميم الاحترافي الجديد ===== */

/* إعادة تعيين شاملة */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* متغيرات النظام الاحترافية */
:root {
  /* الألوان الأساسية - نظام داكن احترافي */
  --color-primary: #0f172a;
  --color-secondary: #1e293b;
  --color-surface: #334155;
  --color-surface-light: #475569;

  /* ألوان النص */
  --text-primary: #f8fafc;
  --text-secondary: #e2e8f0;
  --text-muted: #94a3b8;
  --text-inverse: #0f172a;

  /* ألوان الحالة */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  /* ألوان التهديدات */
  --threat-critical: #dc2626;
  --threat-high: #ea580c;
  --threat-medium: #d97706;
  --threat-low: #059669;

  /* ألوان التمييز */
  --accent-blue: #3b82f6;
  --accent-purple: #8b5cf6;
  --accent-cyan: #06b6d4;
  --accent-emerald: #10b981;

  /* الظلال الاحترافية */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* الحدود */
  --border-width: 1px;
  --border-color: rgba(148, 163, 184, 0.1);
  --border-color-light: rgba(148, 163, 184, 0.2);

  /* نصف القطر */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* المسافات */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* الخطوط */
  --font-family-primary: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', monospace;

  /* أحجام الخط */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;

  /* أوزان الخط */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;

  /* الانتقالات */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* التدرجات الاحترافية */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  --gradient-error: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  --gradient-surface: linear-gradient(145deg, #1e293b 0%, #334155 100%);
}

/* الجسم الرئيسي */
html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--text-base);
  font-weight: var(--font-normal);
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--color-primary);
  direction: rtl;
  text-align: right;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* خلفية احترافية متدرجة */
  background-image:
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.05) 0%, transparent 50%),
    linear-gradient(135deg, var(--color-primary) 0%, #0c1426 100%);
  background-attachment: fixed;
  min-height: 100vh;
}

/* ===== نظام الفئات الاحترافي ===== */

/* الحاوي الرئيسي */
.container {
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

/* فئات الخلفية */
.bg-primary { background-color: var(--color-primary); }
.bg-secondary { background-color: var(--color-secondary); }
.bg-surface { background-color: var(--color-surface); }
.bg-surface-light { background-color: var(--color-surface-light); }

/* خلفيات البطاقات */
.bg-card {
  background: var(--gradient-surface);
  border: var(--border-width) solid var(--border-color);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.bg-card-elevated {
  background: var(--gradient-surface);
  border: var(--border-width) solid var(--border-color-light);
  box-shadow: var(--shadow-xl);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* خلفيات التدرج */
.bg-gradient-primary { background: var(--gradient-primary); }
.bg-gradient-success { background: var(--gradient-success); }
.bg-gradient-warning { background: var(--gradient-warning); }
.bg-gradient-error { background: var(--gradient-error); }

/* ألوان النص */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-inverse { color: var(--text-inverse); }

/* ألوان الحالة */
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }
.text-info { color: var(--color-info); }

/* ألوان التهديدات */
.text-threat-critical { color: var(--threat-critical); }
.text-threat-high { color: var(--threat-high); }
.text-threat-medium { color: var(--threat-medium); }
.text-threat-low { color: var(--threat-low); }

/* خلفيات التهديدات */
.bg-threat-critical {
  background: linear-gradient(135deg, rgba(220, 38, 38, 0.15) 0%, rgba(220, 38, 38, 0.05) 100%);
  border: var(--border-width) solid rgba(220, 38, 38, 0.2);
}

.bg-threat-high {
  background: linear-gradient(135deg, rgba(234, 88, 12, 0.15) 0%, rgba(234, 88, 12, 0.05) 100%);
  border: var(--border-width) solid rgba(234, 88, 12, 0.2);
}

.bg-threat-medium {
  background: linear-gradient(135deg, rgba(217, 119, 6, 0.15) 0%, rgba(217, 119, 6, 0.05) 100%);
  border: var(--border-width) solid rgba(217, 119, 6, 0.2);
}

.bg-threat-low {
  background: linear-gradient(135deg, rgba(5, 150, 105, 0.15) 0%, rgba(5, 150, 105, 0.05) 100%);
  border: var(--border-width) solid rgba(5, 150, 105, 0.2);
}

/* الحدود */
.border { border: var(--border-width) solid var(--border-color); }
.border-light { border: var(--border-width) solid var(--border-color-light); }
.border-none { border: none; }

/* نصف القطر */
.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: 50%; }

/* المسافات - Padding */
.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: 0.75rem; }
.p-4 { padding: var(--spacing-md); }
.p-6 { padding: var(--spacing-lg); }
.p-8 { padding: var(--spacing-xl); }
.p-12 { padding: var(--spacing-2xl); }

.px-2 { padding-left: var(--spacing-sm); padding-right: var(--spacing-sm); }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: var(--spacing-md); padding-right: var(--spacing-md); }
.px-6 { padding-left: var(--spacing-lg); padding-right: var(--spacing-lg); }
.px-8 { padding-left: var(--spacing-xl); padding-right: var(--spacing-xl); }

.py-1 { padding-top: var(--spacing-xs); padding-bottom: var(--spacing-xs); }
.py-2 { padding-top: var(--spacing-sm); padding-bottom: var(--spacing-sm); }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: var(--spacing-md); padding-bottom: var(--spacing-md); }
.py-6 { padding-top: var(--spacing-lg); padding-bottom: var(--spacing-lg); }
.py-8 { padding-top: var(--spacing-xl); padding-bottom: var(--spacing-xl); }

/* المسافات - Margin */
.m-0 { margin: 0; }
.m-2 { margin: var(--spacing-sm); }
.m-4 { margin: var(--spacing-md); }
.m-6 { margin: var(--spacing-lg); }
.m-8 { margin: var(--spacing-xl); }

.mx-auto { margin-left: auto; margin-right: auto; }
.mx-2 { margin-left: var(--spacing-sm); margin-right: var(--spacing-sm); }
.mx-4 { margin-left: var(--spacing-md); margin-right: var(--spacing-md); }

.my-2 { margin-top: var(--spacing-sm); margin-bottom: var(--spacing-sm); }
.my-4 { margin-top: var(--spacing-md); margin-bottom: var(--spacing-md); }
.my-6 { margin-top: var(--spacing-lg); margin-bottom: var(--spacing-lg); }
.my-8 { margin-top: var(--spacing-xl); margin-bottom: var(--spacing-xl); }

.mt-2 { margin-top: var(--spacing-sm); }
.mt-4 { margin-top: var(--spacing-md); }
.mt-6 { margin-top: var(--spacing-lg); }
.mt-8 { margin-top: var(--spacing-xl); }

.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-4 { margin-bottom: var(--spacing-md); }
.mb-6 { margin-bottom: var(--spacing-lg); }
.mb-8 { margin-bottom: var(--spacing-xl); }

.mr-2 { margin-right: var(--spacing-sm); }
.mr-4 { margin-right: var(--spacing-md); }
.ml-2 { margin-left: var(--spacing-sm); }
.ml-4 { margin-left: var(--spacing-md); }

/* التخطيط والعرض */
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.block { display: block; }
.inline-block { display: inline-block; }
.hidden { display: none; }

/* Flexbox */
.flex-row { flex-direction: row; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }
.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-none { flex: none; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

/* Grid */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }

/* Gap */
.gap-1 { gap: var(--spacing-xs); }
.gap-2 { gap: var(--spacing-sm); }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: var(--spacing-md); }
.gap-6 { gap: var(--spacing-lg); }
.gap-8 { gap: var(--spacing-xl); }

/* أحجام النص */
.text-xs { font-size: var(--text-xs); line-height: 1.2; }
.text-sm { font-size: var(--text-sm); line-height: 1.3; }
.text-base { font-size: var(--text-base); line-height: 1.5; }
.text-lg { font-size: var(--text-lg); line-height: 1.4; }
.text-xl { font-size: var(--text-xl); line-height: 1.4; }
.text-2xl { font-size: var(--text-2xl); line-height: 1.3; }
.text-3xl { font-size: var(--text-3xl); line-height: 1.2; }
.text-4xl { font-size: var(--text-4xl); line-height: 1.1; }

/* أوزان الخط */
.font-light { font-weight: var(--font-light); }
.font-normal { font-weight: var(--font-normal); }
.font-medium { font-weight: var(--font-medium); }
.font-semibold { font-weight: var(--font-semibold); }
.font-bold { font-weight: var(--font-bold); }
.font-extrabold { font-weight: var(--font-extrabold); }

/* محاذاة النص */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

/* المسافات بين العناصر */
.space-y-1 > * + * { margin-top: var(--spacing-xs); }
.space-y-2 > * + * { margin-top: var(--spacing-sm); }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: var(--spacing-md); }
.space-y-6 > * + * { margin-top: var(--spacing-lg); }
.space-y-8 > * + * { margin-top: var(--spacing-xl); }

.space-x-2 > * + * { margin-right: var(--spacing-sm); }
.space-x-3 > * + * { margin-right: 0.75rem; }
.space-x-4 > * + * { margin-right: var(--spacing-md); }

/* الأبعاد */
.w-auto { width: auto; }
.w-full { width: 100%; }
.w-fit { width: fit-content; }
.w-screen { width: 100vw; }

.h-auto { height: auto; }
.h-full { height: 100%; }
.h-screen { height: 100vh; }
.min-h-screen { min-height: 100vh; }
.min-h-full { min-height: 100%; }

.max-w-xs { max-width: 20rem; }
.max-w-sm { max-width: 24rem; }
.max-w-md { max-width: 28rem; }
.max-w-lg { max-width: 32rem; }
.max-w-xl { max-width: 36rem; }
.max-w-2xl { max-width: 42rem; }
.max-w-4xl { max-width: 56rem; }
.max-w-6xl { max-width: 72rem; }
.max-w-7xl { max-width: 80rem; }

/* الشفافية */
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* الظلال */
.shadow-xs { box-shadow: var(--shadow-xs); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow-md); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }
.shadow-none { box-shadow: none; }

/* التأثيرات والانتقالات */
.transition-none { transition: none; }
.transition-all { transition: all var(--transition-normal); }
.transition-colors {
  transition: color var(--transition-normal),
              background-color var(--transition-normal),
              border-color var(--transition-normal);
}
.transition-opacity { transition: opacity var(--transition-normal); }
.transition-shadow { transition: box-shadow var(--transition-normal); }
.transition-transform { transition: transform var(--transition-normal); }

.duration-75 { transition-duration: 75ms; }
.duration-100 { transition-duration: 100ms; }
.duration-150 { transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }
.duration-500 { transition-duration: 500ms; }

.ease-linear { transition-timing-function: linear; }
.ease-in { transition-timing-function: cubic-bezier(0.4, 0, 1, 1); }
.ease-out { transition-timing-function: cubic-bezier(0, 0, 0.2, 1); }
.ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }

/* التحويلات */
.transform { transform: translateZ(0); }
.scale-90 { transform: scale(0.9); }
.scale-95 { transform: scale(0.95); }
.scale-100 { transform: scale(1); }
.scale-105 { transform: scale(1.05); }
.scale-110 { transform: scale(1.1); }

.translate-y-0 { transform: translateY(0); }
.-translate-y-1 { transform: translateY(-0.25rem); }
.-translate-y-2 { transform: translateY(-0.5rem); }
.translate-y-1 { transform: translateY(0.25rem); }
.translate-y-2 { transform: translateY(0.5rem); }

/* التفاعل */
.cursor-auto { cursor: auto; }
.cursor-default { cursor: default; }
.cursor-pointer { cursor: pointer; }
.cursor-wait { cursor: wait; }
.cursor-text { cursor: text; }
.cursor-move { cursor: move; }
.cursor-not-allowed { cursor: not-allowed; }

.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }
.select-auto { user-select: auto; }

/* ===== التأثيرات التفاعلية ===== */

/* تأثيرات التمرير */
.hover\:bg-surface:hover { background-color: var(--color-surface); }
.hover\:bg-surface-light:hover { background-color: var(--color-surface-light); }
.hover\:text-primary:hover { color: var(--text-primary); }
.hover\:text-secondary:hover { color: var(--text-secondary); }
.hover\:scale-105:hover { transform: scale(1.05); }
.hover\:scale-110:hover { transform: scale(1.1); }
.hover\:-translate-y-1:hover { transform: translateY(-0.25rem); }
.hover\:shadow-lg:hover { box-shadow: var(--shadow-lg); }
.hover\:shadow-xl:hover { box-shadow: var(--shadow-xl); }
.hover\:opacity-80:hover { opacity: 0.8; }
.hover\:opacity-90:hover { opacity: 0.9; }

/* تأثيرات التركيز */
.focus\:outline-none:focus { outline: none; }
.focus\:ring-2:focus { box-shadow: 0 0 0 2px var(--accent-blue); }
.focus\:ring-4:focus { box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3); }
.focus\:border-blue:focus { border-color: var(--accent-blue); }

/* تأثيرات النشاط */
.active\:scale-95:active { transform: scale(0.95); }
.active\:bg-surface:active { background-color: var(--color-surface); }

/* حالة التعطيل */
.disabled\:opacity-50:disabled { opacity: 0.5; }
.disabled\:cursor-not-allowed:disabled { cursor: not-allowed; }
.disabled\:pointer-events-none:disabled { pointer-events: none; }

/* ===== الأنيميشن والحركة ===== */

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(2rem);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-2rem);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
  }
}

/* فئات الأنيميشن */
.animate-fadeIn {
  animation: fadeIn 0.6s ease-out;
}

.animate-slideInRight {
  animation: slideInRight 0.5s ease-out;
}

.animate-slideInLeft {
  animation: slideInLeft 0.5s ease-out;
}

.animate-scaleIn {
  animation: scaleIn 0.4s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* تأخير الأنيميشن */
.delay-75 { animation-delay: 75ms; }
.delay-100 { animation-delay: 100ms; }
.delay-150 { animation-delay: 150ms; }
.delay-200 { animation-delay: 200ms; }
.delay-300 { animation-delay: 300ms; }
.delay-500 { animation-delay: 500ms; }

/* ===== الاستجابة للشاشات ===== */

/* الشاشات الصغيرة - 640px+ */
@media (min-width: 640px) {
  .sm\:block { display: block; }
  .sm\:flex { display: flex; }
  .sm\:grid { display: grid; }
  .sm\:hidden { display: none; }

  .sm\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .sm\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }

  .sm\:text-sm { font-size: var(--text-sm); }
  .sm\:text-base { font-size: var(--text-base); }
  .sm\:text-lg { font-size: var(--text-lg); }
  .sm\:text-xl { font-size: var(--text-xl); }

  .sm\:p-4 { padding: var(--spacing-md); }
  .sm\:p-6 { padding: var(--spacing-lg); }
  .sm\:p-8 { padding: var(--spacing-xl); }

  .sm\:gap-4 { gap: var(--spacing-md); }
  .sm\:gap-6 { gap: var(--spacing-lg); }
  .sm\:gap-8 { gap: var(--spacing-xl); }
}

/* الشاشات المتوسطة - 768px+ */
@media (min-width: 768px) {
  .md\:block { display: block; }
  .md\:flex { display: flex; }
  .md\:grid { display: grid; }
  .md\:hidden { display: none; }

  .md\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

  .md\:text-base { font-size: var(--text-base); }
  .md\:text-lg { font-size: var(--text-lg); }
  .md\:text-xl { font-size: var(--text-xl); }
  .md\:text-2xl { font-size: var(--text-2xl); }
  .md\:text-3xl { font-size: var(--text-3xl); }

  .md\:p-6 { padding: var(--spacing-lg); }
  .md\:p-8 { padding: var(--spacing-xl); }
  .md\:p-12 { padding: var(--spacing-2xl); }

  .md\:gap-6 { gap: var(--spacing-lg); }
  .md\:gap-8 { gap: var(--spacing-xl); }
}

/* الشاشات الكبيرة - 1024px+ */
@media (min-width: 1024px) {
  .lg\:block { display: block; }
  .lg\:flex { display: flex; }
  .lg\:grid { display: grid; }
  .lg\:hidden { display: none; }

  .lg\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }

  .lg\:text-lg { font-size: var(--text-lg); }
  .lg\:text-xl { font-size: var(--text-xl); }
  .lg\:text-2xl { font-size: var(--text-2xl); }
  .lg\:text-3xl { font-size: var(--text-3xl); }
  .lg\:text-4xl { font-size: var(--text-4xl); }
}

/* الشاشات الكبيرة جداً - 1280px+ */
@media (min-width: 1280px) {
  .xl\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .xl\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .xl\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
  .xl\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }

  .xl\:text-2xl { font-size: var(--text-2xl); }
  .xl\:text-3xl { font-size: var(--text-3xl); }
  .xl\:text-4xl { font-size: var(--text-4xl); }
}

/* الشاشات الضخمة - 1536px+ */
@media (min-width: 1536px) {
  .\32xl\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .\32xl\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
  .\32xl\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
  .\32xl\:grid-cols-7 { grid-template-columns: repeat(7, minmax(0, 1fr)); }
}

/* ===== فئات مساعدة احترافية ===== */

/* تحسينات الأداء */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
}

/* فئات إمكانية الوصول */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* تحسينات شريط التمرير */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--color-secondary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--color-surface);
  border-radius: 3px;
  transition: background var(--transition-normal);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-surface-light);
}

::-webkit-scrollbar-corner {
  background: var(--color-secondary);
}

/* تحسينات للطباعة */
@media print {
  *,
  *::before,
  *::after {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  .no-print {
    display: none !important;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
  }
}

/* تحسينات للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* تحسينات للتباين العالي */
@media (prefers-contrast: high) {
  :root {
    --border-color: rgba(255, 255, 255, 0.4);
    --border-color-light: rgba(255, 255, 255, 0.6);
  }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 480px) {
  .container {
    padding: 0 var(--spacing-md);
  }

  .text-4xl { font-size: var(--text-2xl); }
  .text-3xl { font-size: var(--text-xl); }
  .text-2xl { font-size: var(--text-lg); }
}
