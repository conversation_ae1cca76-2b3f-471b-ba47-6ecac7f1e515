/* استيراد Tailwind CSS */
/* أنماط CSS أساسية للنظام */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: '<PERSON><PERSON><PERSON>', 'Cairo', '<PERSON><PERSON>', sans-serif;
  background-color: #111827;
  color: #f9fafb;
  direction: rtl;
  text-align: right;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.bg-gray-900 { background-color: #111827; }
.bg-gray-800 { background-color: #1f2937; }
.bg-gray-700 { background-color: #374151; }
.bg-gray-600 { background-color: #4b5563; }

.text-white { color: #ffffff; }
.text-gray-200 { color: #e5e7eb; }
.text-gray-300 { color: #d1d5db; }
.text-gray-400 { color: #9ca3af; }

.text-red-400 { color: #f87171; }
.text-orange-400 { color: #fb923c; }
.text-yellow-400 { color: #facc15; }
.text-green-400 { color: #4ade80; }
.text-blue-400 { color: #60a5fa; }

.bg-red-900 { background-color: #7f1d1d; }
.bg-orange-900 { background-color: #7c2d12; }
.bg-yellow-900 { background-color: #713f12; }
.bg-green-900 { background-color: #14532d; }
.bg-blue-900 { background-color: #1e3a8a; }

.border { border: 1px solid #374151; }
.border-red-500 { border-color: #ef4444; }
.border-orange-500 { border-color: #f97316; }
.border-yellow-500 { border-color: #eab308; }
.border-green-500 { border-color: #22c55e; }
.border-blue-500 { border-color: #3b82f6; }

.rounded { border-radius: 0.25rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }

.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }

.m-2 { margin: 0.5rem; }
.m-4 { margin: 1rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mr-2 { margin-right: 0.5rem; }

.flex { display: flex; }
.grid { display: grid; }
.hidden { display: none; }
.block { display: block; }

.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }

.gap-2 { gap: 0.5rem; }
.gap-4 { gap: 1rem; }

.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }

.hover\:bg-gray-600:hover { background-color: #4b5563; }
.hover\:bg-blue-700:hover { background-color: #1d4ed8; }
.hover\:bg-green-700:hover { background-color: #15803d; }

.transition { transition: all 0.15s ease-in-out; }

.cursor-pointer { cursor: pointer; }

.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }

.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }

@media (min-width: 640px) {
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .sm\:text-lg { font-size: 1.125rem; }
}

@media (min-width: 768px) {
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:text-xl { font-size: 1.25rem; }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\:text-2xl { font-size: 1.5rem; }
}

/* استيراد خطوط عربية من Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

/* إعدادات أساسية للعربية */
:root {
  font-family: 'Tajawal', 'Cairo', system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  font-weight: 400;
  direction: rtl;
  text-align: right;
  
  /* متغيرات CSS مخصصة */
  --color-primary: #3b82f6;
  --color-secondary: #1f2937;
  --color-accent: #f59e0b;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;
  
  /* ألوان الخطورة */
  --severity-critical: #ef4444;
  --severity-high: #f97316;
  --severity-medium: #eab308;
  --severity-low: #22c55e;
  --severity-unknown: #6b7280;
  
  /* خلفيات داكنة */
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --bg-tertiary: #374151;
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-muted: #9ca3af;
}

/* إعدادات عامة للجسم */
body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Tajawal', 'Cairo', sans-serif;
  direction: rtl;
  overflow-x: hidden;
}

/* إعدادات للنصوص العربية */
* {
  box-sizing: border-box;
}

/* تحسين عرض النصوص العربية */
.arabic-text {
  font-family: 'Tajawal', 'Cairo', sans-serif;
  direction: rtl;
  text-align: right;
  line-height: 1.8;
}

/* أنماط مخصصة للمكونات */
.glass-effect {
  background: rgba(31, 41, 55, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(75, 85, 99, 0.3);
}

.gradient-bg {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

.severity-critical {
  color: var(--severity-critical);
  background-color: rgba(239, 68, 68, 0.1);
  border-color: var(--severity-critical);
}

.severity-high {
  color: var(--severity-high);
  background-color: rgba(249, 115, 22, 0.1);
  border-color: var(--severity-high);
}

.severity-medium {
  color: var(--severity-medium);
  background-color: rgba(234, 179, 8, 0.1);
  border-color: var(--severity-medium);
}

.severity-low {
  color: var(--severity-low);
  background-color: rgba(34, 197, 94, 0.1);
  border-color: var(--severity-low);
}

.severity-unknown {
  color: var(--severity-unknown);
  background-color: rgba(107, 114, 128, 0.1);
  border-color: var(--severity-unknown);
}

/* تحسينات للتمرير */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--bg-tertiary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #4b5563;
}

/* تأثيرات الحركة */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    transform: translateY(20px); 
    opacity: 0; 
  }
  to { 
    transform: translateY(0); 
    opacity: 1; 
  }
}

/* تحسينات للطباعة */
@media print {
  body {
    background: white;
    color: black;
  }
  
  .no-print {
    display: none;
  }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  body {
    font-size: 14px;
  }
  
  .container {
    padding: 1rem;
  }
}
