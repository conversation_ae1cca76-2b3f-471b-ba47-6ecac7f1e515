/* استيراد Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* استيراد خطوط عربية من Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

/* إعدادات أساسية للعربية */
:root {
  font-family: 'Tajawal', 'Cairo', system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  font-weight: 400;
  direction: rtl;
  text-align: right;
  
  /* متغيرات CSS مخصصة */
  --color-primary: #3b82f6;
  --color-secondary: #1f2937;
  --color-accent: #f59e0b;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;
  
  /* ألوان الخطورة */
  --severity-critical: #ef4444;
  --severity-high: #f97316;
  --severity-medium: #eab308;
  --severity-low: #22c55e;
  --severity-unknown: #6b7280;
  
  /* خلفيات داكنة */
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --bg-tertiary: #374151;
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-muted: #9ca3af;
}

/* إعدادات عامة للجسم */
body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Tajawal', 'Cairo', sans-serif;
  direction: rtl;
  overflow-x: hidden;
}

/* إعدادات للنصوص العربية */
* {
  box-sizing: border-box;
}

/* تحسين عرض النصوص العربية */
.arabic-text {
  font-family: 'Tajawal', 'Cairo', sans-serif;
  direction: rtl;
  text-align: right;
  line-height: 1.8;
}

/* أنماط مخصصة للمكونات */
.glass-effect {
  background: rgba(31, 41, 55, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(75, 85, 99, 0.3);
}

.gradient-bg {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

.severity-critical {
  color: var(--severity-critical);
  background-color: rgba(239, 68, 68, 0.1);
  border-color: var(--severity-critical);
}

.severity-high {
  color: var(--severity-high);
  background-color: rgba(249, 115, 22, 0.1);
  border-color: var(--severity-high);
}

.severity-medium {
  color: var(--severity-medium);
  background-color: rgba(234, 179, 8, 0.1);
  border-color: var(--severity-medium);
}

.severity-low {
  color: var(--severity-low);
  background-color: rgba(34, 197, 94, 0.1);
  border-color: var(--severity-low);
}

.severity-unknown {
  color: var(--severity-unknown);
  background-color: rgba(107, 114, 128, 0.1);
  border-color: var(--severity-unknown);
}

/* تحسينات للتمرير */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--bg-tertiary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #4b5563;
}

/* تأثيرات الحركة */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    transform: translateY(20px); 
    opacity: 0; 
  }
  to { 
    transform: translateY(0); 
    opacity: 1; 
  }
}

/* تحسينات للطباعة */
@media print {
  body {
    background: white;
    color: black;
  }
  
  .no-print {
    display: none;
  }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  body {
    font-size: 14px;
  }
  
  .container {
    padding: 1rem;
  }
}
