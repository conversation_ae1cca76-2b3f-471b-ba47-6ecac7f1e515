<template>
  <div class="sources-status">
    <div class="sources-header">
      <h3>حالة المصادر</h3>
      <span class="sources-count">{{ connectedSources }}/{{ totalSources }}</span>
    </div>
    
    <div class="sources-list">
      <div 
        v-for="source in sources" 
        :key="source.id"
        class="source-item"
      >
        <div class="source-status" :class="source.status"></div>
        <div class="source-content">
          <div class="source-name">{{ source.name }}</div>
          <div class="source-meta">
            <span class="source-threats">{{ source.threatsCount }} تهديد</span>
            <span v-if="source.responseTime" class="source-latency">{{ source.responseTime }}ms</span>
          </div>
        </div>
        <div class="source-indicator">
          <span v-if="source.status === 'connected'">🟢</span>
          <span v-else-if="source.status === 'maintenance'">🟡</span>
          <span v-else>🔴</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useThreatsStore } from '../../stores/threats'

export default {
  name: 'SourcesStatus',
  setup() {
    const threatsStore = useThreatsStore()
    
    const sources = computed(() => threatsStore.sources)
    const connectedSources = computed(() => threatsStore.connectedSources)
    const totalSources = computed(() => threatsStore.totalSources)
    
    return {
      sources,
      connectedSources,
      totalSources
    }
  }
}
</script>

<style lang="scss" scoped>
.sources-status {
  background: $gradient-bg-secondary;
  border: 1px solid $border-color-light;
  border-radius: $border-radius-2xl;
  padding: $spacing-xl;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sources-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-lg;
  
  h3 {
    font-size: $font-size-xl;
    font-weight: $font-weight-bold;
    color: $text-primary;
  }
}

.sources-count {
  font-size: $font-size-sm;
  color: $color-success;
  font-family: $font-family-mono;
  font-weight: $font-weight-bold;
}

.sources-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
}

.source-item {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  padding: $spacing-md;
  background: rgba(10, 15, 28, 0.3);
  border-radius: $border-radius-lg;
  transition: all $transition-normal;
  
  &:hover {
    background: rgba(10, 15, 28, 0.5);
    transform: translateX(-2px);
  }
}

.source-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  
  &.connected {
    background: $color-success;
    animation: pulse 2s infinite;
  }
  
  &.maintenance {
    background: $color-warning;
  }
  
  &.disconnected {
    background: $color-error;
  }
}

.source-content {
  flex: 1;
}

.source-name {
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  color: $text-secondary;
  margin-bottom: 2px;
}

.source-meta {
  display: flex;
  gap: $spacing-sm;
  font-size: $font-size-xs;
  color: $text-muted;
}

.source-indicator {
  font-size: 12px;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
</style>
