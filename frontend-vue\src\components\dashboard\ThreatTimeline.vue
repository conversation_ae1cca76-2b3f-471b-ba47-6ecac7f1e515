<template>
  <div class="threat-timeline">
    <div class="timeline-header">
      <h3>الخط الزمني للتهديدات</h3>
      <span class="timeline-period">آخر 7 أيام</span>
    </div>
    
    <div class="timeline-chart">
      <svg viewBox="0 0 300 150" class="chart-svg">
        <defs>
          <linearGradient id="timelineGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style="stop-color:#4f8ff7;stop-opacity:0.8" />
            <stop offset="100%" style="stop-color:#4f8ff7;stop-opacity:0.1" />
          </linearGradient>
        </defs>
        
        <!-- Grid lines -->
        <g class="grid-lines">
          <line v-for="i in 5" :key="i" 
                :x1="0" :y1="i * 30" 
                :x2="300" :y2="i * 30" 
                stroke="rgba(79, 143, 247, 0.1)" 
                stroke-width="1"/>
        </g>
        
        <!-- Timeline path -->
        <path 
          :d="timelinePath" 
          fill="url(#timelineGradient)" 
          stroke="#4f8ff7" 
          stroke-width="2"
        />
        
        <!-- Data points -->
        <circle 
          v-for="(point, index) in timelinePoints" 
          :key="index"
          :cx="point.x" 
          :cy="point.y" 
          r="4"
          fill="#4f8ff7"
          class="data-point"
        />
      </svg>
    </div>
    
    <div class="timeline-stats">
      <div class="stat-item">
        <span class="stat-label">اليوم</span>
        <span class="stat-value">{{ todayThreats }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">أمس</span>
        <span class="stat-value">{{ yesterdayThreats }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">المتوسط</span>
        <span class="stat-value">{{ averageThreats }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useThreatsStore } from '../../stores/threats'

export default {
  name: 'ThreatTimeline',
  setup() {
    const threatsStore = useThreatsStore()
    
    const timelineData = computed(() => threatsStore.getThreatsTimeline(7))
    
    const timelinePoints = computed(() => {
      const data = timelineData.value
      if (data.length === 0) return []
      
      const maxCount = Math.max(...data.map(d => d.count))
      const width = 300
      const height = 150
      const padding = 20
      
      return data.map((item, index) => ({
        x: (index / (data.length - 1)) * (width - padding * 2) + padding,
        y: height - padding - ((item.count / maxCount) * (height - padding * 2))
      }))
    })
    
    const timelinePath = computed(() => {
      const points = timelinePoints.value
      if (points.length === 0) return ''
      
      let path = `M ${points[0].x} 150`
      points.forEach(point => {
        path += ` L ${point.x} ${point.y}`
      })
      path += ` L ${points[points.length - 1].x} 150 Z`
      
      return path
    })
    
    const todayThreats = computed(() => {
      const today = timelineData.value[timelineData.value.length - 1]
      return today ? today.count : 0
    })
    
    const yesterdayThreats = computed(() => {
      const yesterday = timelineData.value[timelineData.value.length - 2]
      return yesterday ? yesterday.count : 0
    })
    
    const averageThreats = computed(() => {
      const data = timelineData.value
      if (data.length === 0) return 0
      const total = data.reduce((sum, item) => sum + item.count, 0)
      return Math.round(total / data.length)
    })
    
    return {
      timelinePoints,
      timelinePath,
      todayThreats,
      yesterdayThreats,
      averageThreats
    }
  }
}
</script>

<style lang="scss" scoped>
.threat-timeline {
  background: $gradient-bg-secondary;
  border: 1px solid $border-color-light;
  border-radius: $border-radius-2xl;
  padding: $spacing-xl;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-lg;
  
  h3 {
    font-size: $font-size-xl;
    font-weight: $font-weight-bold;
    color: $text-primary;
  }
}

.timeline-period {
  font-size: $font-size-sm;
  color: $text-muted;
  font-family: $font-family-mono;
}

.timeline-chart {
  flex: 1;
  margin-bottom: $spacing-lg;
}

.chart-svg {
  width: 100%;
  height: 100%;
}

.data-point {
  transition: all $transition-normal;
  cursor: pointer;
  
  &:hover {
    r: 6;
    fill: #60a5fa;
  }
}

.timeline-stats {
  display: flex;
  justify-content: space-between;
  padding-top: $spacing-md;
  border-top: 1px solid $border-color;
}

.stat-item {
  text-align: center;
  
  .stat-label {
    display: block;
    font-size: $font-size-xs;
    color: $text-muted;
    margin-bottom: 2px;
  }
  
  .stat-value {
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
    color: $text-primary;
    font-family: $font-family-mono;
  }
}
</style>
