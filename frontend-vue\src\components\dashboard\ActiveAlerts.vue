<template>
  <div class="active-alerts">
    <div class="alerts-header">
      <h3>التنبيهات النشطة</h3>
      <div class="header-actions">
        <span class="alerts-count">{{ activeAlerts.length }} تنبيه</span>
        <button class="view-all-btn">عرض الكل</button>
      </div>
    </div>
    
    <div class="alerts-list">
      <div 
        v-for="alert in activeAlerts.slice(0, 5)" 
        :key="alert.id"
        class="alert-item"
        :class="alert.priority"
      >
        <div class="alert-icon">
          <span v-if="alert.priority === 'critical'">🔴</span>
          <span v-else-if="alert.priority === 'high'">🟡</span>
          <span v-else-if="alert.priority === 'medium'">🟠</span>
          <span v-else>🟢</span>
        </div>
        
        <div class="alert-content">
          <div class="alert-title">{{ alert.title }}</div>
          <div class="alert-message">{{ alert.message }}</div>
          <div class="alert-meta">
            <span class="alert-time">{{ formatTime(alert.timestamp) }}</span>
            <span class="alert-source">{{ alert.source }}</span>
          </div>
        </div>
        
        <div class="alert-actions">
          <button class="action-btn" @click="acknowledgeAlert(alert.id)">
            ✓
          </button>
          <button class="action-btn" @click="dismissAlert(alert.id)">
            ✕
          </button>
        </div>
      </div>
      
      <div v-if="activeAlerts.length === 0" class="no-alerts">
        <div class="no-alerts-icon">✅</div>
        <div class="no-alerts-text">لا توجد تنبيهات نشطة</div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useThreatsStore } from '../../stores/threats'

export default {
  name: 'ActiveAlerts',
  setup() {
    const threatsStore = useThreatsStore()
    
    const activeAlerts = computed(() => 
      threatsStore.alerts.filter(alert => alert.status === 'new' || alert.status === 'acknowledged')
    )
    
    const formatTime = (timestamp) => {
      const now = new Date()
      const alertTime = new Date(timestamp)
      const diffMinutes = Math.floor((now - alertTime) / (1000 * 60))
      
      if (diffMinutes < 1) return 'الآن'
      if (diffMinutes < 60) return `منذ ${diffMinutes} دقيقة`
      
      const diffHours = Math.floor(diffMinutes / 60)
      if (diffHours < 24) return `منذ ${diffHours} ساعة`
      
      const diffDays = Math.floor(diffHours / 24)
      return `منذ ${diffDays} يوم`
    }
    
    const acknowledgeAlert = (id) => {
      // تأكيد التنبيه
      console.log('تأكيد التنبيه:', id)
    }
    
    const dismissAlert = (id) => {
      threatsStore.dismissAlert(id)
    }
    
    return {
      activeAlerts,
      formatTime,
      acknowledgeAlert,
      dismissAlert
    }
  }
}
</script>

<style lang="scss" scoped>
.active-alerts {
  background: $gradient-bg-secondary;
  border: 1px solid $border-color-light;
  border-radius: $border-radius-2xl;
  padding: $spacing-xl;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.alerts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-lg;
  
  h3 {
    font-size: $font-size-xl;
    font-weight: $font-weight-bold;
    color: $text-primary;
  }
}

.header-actions {
  display: flex;
  align-items: center;
  gap: $spacing-md;
}

.alerts-count {
  font-size: $font-size-sm;
  color: $text-muted;
  font-family: $font-family-mono;
}

.view-all-btn {
  padding: $spacing-xs $spacing-sm;
  background: rgba(79, 143, 247, 0.2);
  border: 1px solid $color-accent-blue;
  border-radius: $border-radius-lg;
  color: $color-accent-blue;
  font-size: $font-size-xs;
  cursor: pointer;
  transition: all $transition-normal;
  
  &:hover {
    background: $color-accent-blue;
    color: white;
  }
}

.alerts-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  gap: $spacing-md;
  padding: $spacing-md;
  background: rgba(10, 15, 28, 0.3);
  border: 1px solid $border-color;
  border-radius: $border-radius-lg;
  transition: all $transition-normal;
  border-right: 4px solid transparent;
  
  &:hover {
    background: rgba(10, 15, 28, 0.5);
    transform: translateX(-2px);
  }
  
  &.critical {
    border-right-color: $color-error;
  }
  
  &.high {
    border-right-color: $color-warning;
  }
  
  &.medium {
    border-right-color: #f97316;
  }
  
  &.low {
    border-right-color: $color-success;
  }
}

.alert-icon {
  font-size: 16px;
  margin-top: 2px;
}

.alert-content {
  flex: 1;
  min-width: 0;
}

.alert-title {
  font-size: $font-size-sm;
  font-weight: $font-weight-semibold;
  color: $text-primary;
  margin-bottom: 2px;
}

.alert-message {
  font-size: $font-size-xs;
  color: $text-secondary;
  margin-bottom: $spacing-xs;
  line-height: 1.4;
}

.alert-meta {
  display: flex;
  gap: $spacing-sm;
  font-size: 10px;
  color: $text-muted;
}

.alert-actions {
  display: flex;
  gap: $spacing-xs;
}

.action-btn {
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid $border-color;
  border-radius: 6px;
  color: $text-muted;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all $transition-fast;
  
  &:hover {
    background: rgba(255, 255, 255, 0.2);
    color: $text-secondary;
  }
}

.no-alerts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  text-align: center;
  padding: $spacing-xl;
}

.no-alerts-icon {
  font-size: 48px;
  margin-bottom: $spacing-md;
}

.no-alerts-text {
  font-size: $font-size-lg;
  color: $text-tertiary;
  font-weight: $font-weight-medium;
}
</style>
