<template>
  <div class="recent-activity">
    <div class="activity-header">
      <h3>النشاط الأخير</h3>
      <span class="activity-count">{{ activities.length }} حدث</span>
    </div>
    
    <div class="activity-timeline">
      <div 
        v-for="activity in activities.slice(0, 8)" 
        :key="activity.id"
        class="activity-item"
      >
        <div class="activity-icon">{{ activity.icon }}</div>
        <div class="activity-content">
          <div class="activity-title">{{ activity.title }}</div>
          <div class="activity-time">{{ formatTime(activity.timestamp) }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useThreatsStore } from '../../stores/threats'

export default {
  name: 'RecentActivity',
  setup() {
    const threatsStore = useThreatsStore()
    
    const activities = computed(() => threatsStore.getRecentActivity())
    
    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }
    
    return {
      activities,
      formatTime
    }
  }
}
</script>

<style lang="scss" scoped>
.recent-activity {
  background: $gradient-bg-secondary;
  border: 1px solid $border-color-light;
  border-radius: $border-radius-2xl;
  padding: $spacing-xl;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-lg;
  
  h3 {
    font-size: $font-size-xl;
    font-weight: $font-weight-bold;
    color: $text-primary;
  }
}

.activity-count {
  font-size: $font-size-sm;
  color: $text-muted;
  font-family: $font-family-mono;
}

.activity-timeline {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  padding: $spacing-md;
  background: rgba(10, 15, 28, 0.3);
  border-radius: $border-radius-lg;
  transition: all $transition-normal;
  
  &:hover {
    background: rgba(10, 15, 28, 0.5);
    transform: translateX(-2px);
  }
}

.activity-icon {
  font-size: 20px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(79, 143, 247, 0.2);
  border-radius: $border-radius-lg;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: $font-size-sm;
  color: $text-secondary;
  margin-bottom: 2px;
}

.activity-time {
  font-size: $font-size-xs;
  color: $text-muted;
  font-family: $font-family-mono;
}
</style>
