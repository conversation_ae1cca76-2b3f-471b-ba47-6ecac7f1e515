import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'

import App from './App.vue'
import './styles/main.scss'

// استيراد المكونات
import Dashboard from './views/Dashboard.vue'

// إعداد التوجيه المبسط
const routes = [
  { path: '/', name: 'dashboard', component: Dashboard }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// إعداد Pinia للحالة
const pinia = createPinia()

// إنشاء التطبيق
const app = createApp(App)

app.use(pinia)
app.use(router)

app.mount('#app')
