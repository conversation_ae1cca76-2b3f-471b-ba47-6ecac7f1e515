<template>
  <div id="app" class="app-container">
    <!-- شريط علوي احترافي -->
    <header class="app-header">
      <div class="container">
        <div class="header-content">
          <div class="brand">
            <div class="brand-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="brand-text">
              <h1 class="brand-title">SecureCheck</h1>
              <span class="brand-subtitle">Advanced Malware Analysis</span>
            </div>
          </div>

          <div class="header-metrics">
            <div class="metric">
              <div class="metric-value">{{ totalScans }}</div>
              <div class="metric-label">Total Scans</div>
            </div>
            <div class="metric-divider"></div>
            <div class="metric">
              <div class="metric-value">{{ threatsFound }}</div>
              <div class="metric-label">Threats Detected</div>
            </div>
            <div class="metric-divider"></div>
            <div class="metric">
              <div class="metric-value">{{ successRate }}%</div>
              <div class="metric-label">Success Rate</div>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="app-main">
      <div class="container">
        <router-view v-slot="{ Component }">
          <transition name="page-transition" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </div>
    </main>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useVirusStore } from './stores/virus'

export default {
  name: 'App',
  setup() {
    const virusStore = useVirusStore()

    const totalScans = computed(() => virusStore.totalScans)
    const threatsFound = computed(() => virusStore.threatsFound)
    const successRate = computed(() => {
      if (virusStore.totalScans === 0) return 100
      const cleanScans = virusStore.totalScans - virusStore.threatsFound
      return Math.round((cleanScans / virusStore.totalScans) * 100)
    })

    return {
      totalScans,
      threatsFound,
      successRate
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, $color-primary 0%, $color-secondary 100%);
  display: flex;
  flex-direction: column;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
}

// Header احترافي
.app-header {
  background: rgba($color-secondary, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba($color-accent-blue, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba($color-accent-blue, 0.3), transparent);
  }
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 0;
  gap: 2rem;
}

.brand {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.brand-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, $color-accent-blue, $color-accent-purple);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(135deg, $color-accent-blue, $color-accent-purple);
    border-radius: 14px;
    z-index: -1;
    opacity: 0.3;
    filter: blur(8px);
  }

  svg {
    width: 24px;
    height: 24px;
  }
}

.brand-text {
  .brand-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: $text-primary;
    margin: 0;
    letter-spacing: -0.025em;
  }

  .brand-subtitle {
    font-size: 0.875rem;
    color: $text-tertiary;
    font-weight: 500;
  }
}

.header-metrics {
  display: flex;
  align-items: center;
  gap: 2rem;
  background: rgba($color-primary, 0.5);
  padding: 1rem 1.5rem;
  border-radius: 16px;
  border: 1px solid rgba($color-accent-blue, 0.1);
}

.metric {
  text-align: center;

  .metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: $text-primary;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', monospace;
    line-height: 1;
  }

  .metric-label {
    font-size: 0.75rem;
    color: $text-tertiary;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-top: 0.25rem;
  }
}

.metric-divider {
  width: 1px;
  height: 2rem;
  background: rgba($color-accent-blue, 0.2);
}

// Main content
.app-main {
  flex: 1;
  padding: 3rem 0;
}

// Transitions احترافية
.page-transition-enter-active,
.page-transition-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-transition-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.page-transition-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

// Responsive design
@media (max-width: 1024px) {
  .header-content {
    flex-direction: column;
    gap: 1.5rem;
  }

  .header-metrics {
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .app-main {
    padding: 2rem 0;
  }

  .header-metrics {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .metric-divider {
    width: 2rem;
    height: 1px;
  }

  .brand-text .brand-title {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .brand {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .header-metrics {
    width: 100%;
  }
}
</style>
