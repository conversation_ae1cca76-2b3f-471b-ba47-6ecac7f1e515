<template>
  <div id="app" class="app-container">
    <!-- شريط علوي مبسط -->
    <header class="top-header">
      <div class="header-content">
        <div class="logo-section">
          <div class="logo">🛡️</div>
          <h1>محلل الفيروسات</h1>
        </div>

        <div class="header-stats">
          <div class="stat-item">
            <span class="stat-value">{{ totalScans }}</span>
            <span class="stat-label">إجمالي الفحوصات</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ threatsFound }}</span>
            <span class="stat-label">تهديدات مكتشفة</span>
          </div>
        </div>
      </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </main>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useVirusStore } from './stores/virus'

export default {
  name: 'App',
  setup() {
    const virusStore = useVirusStore()

    const totalScans = computed(() => virusStore.totalScans)
    const threatsFound = computed(() => virusStore.threatsFound)

    return {
      totalScans,
      threatsFound
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0f1c 0%, #1a2332 100%);
}

.top-header {
  background: rgba(26, 35, 50, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(79, 143, 247, 0.2);
  padding: $spacing-lg $spacing-xl;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: $spacing-md;

  .logo {
    font-size: 2rem;
  }

  h1 {
    font-size: $font-size-2xl;
    font-weight: $font-weight-bold;
    color: $text-primary;
    margin: 0;
  }
}

.header-stats {
  display: flex;
  gap: $spacing-xl;
}

.stat-item {
  text-align: center;

  .stat-value {
    display: block;
    font-size: $font-size-2xl;
    font-weight: $font-weight-bold;
    color: $color-accent-blue;
    font-family: $font-family-mono;
  }

  .stat-label {
    font-size: $font-size-sm;
    color: $text-muted;
  }
}

.main-content {
  flex: 1;
  padding: $spacing-xl;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

// انتقالات بسيطة
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// استجابة للشاشات
@media (max-width: $breakpoint-md) {
  .header-content {
    flex-direction: column;
    gap: $spacing-lg;
  }

  .header-stats {
    gap: $spacing-lg;
  }

  .main-content {
    padding: $spacing-lg;
  }
}
</style>
