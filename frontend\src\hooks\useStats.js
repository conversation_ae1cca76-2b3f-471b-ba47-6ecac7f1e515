/**
 * Hook مخصص لإدارة الإحصائيات
 */

import { useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { statsAPI, systemAPI, handleAPIError } from '../services/api';

/**
 * Hook لجلب الإحصائيات العامة
 */
export const useStats = (options = {}) => {
  return useQuery({
    queryKey: ['stats'],
    queryFn: statsAPI.getStatistics,
    staleTime: 5 * 60 * 1000, // 5 دقائق
    cacheTime: 10 * 60 * 1000, // 10 دقائق
    refetchInterval: 10 * 60 * 1000, // تحديث كل 10 دقائق
    refetchOnWindowFocus: false,
    retry: 3,
    onError: (error) => {
      console.error('خطأ في جلب الإحصائيات:', handleAPIError(error));
    },
    ...options
  });
};

/**
 * Hook لجلب حالة النظام
 */
export const useSystemStatus = (options = {}) => {
  return useQuery({
    queryKey: ['systemStatus'],
    queryFn: systemAPI.getSystemStatus,
    staleTime: 2 * 60 * 1000, // دقيقتان
    cacheTime: 5 * 60 * 1000, // 5 دقائق
    refetchInterval: 5 * 60 * 1000, // تحديث كل 5 دقائق
    refetchOnWindowFocus: false,
    retry: 2,
    onError: (error) => {
      console.error('خطأ في جلب حالة النظام:', handleAPIError(error));
    },
    ...options
  });
};

/**
 * Hook لحساب الإحصائيات المحلية من البيانات
 */
export const useLocalStats = (threats = []) => {
  const stats = useMemo(() => {
    if (!threats || threats.length === 0) {
      return {
        total: 0,
        bySeverity: {
          'حرج': 0,
          'عالي': 0,
          'متوسط': 0,
          'منخفض': 0,
          'غير محدد': 0
        },
        byCategory: {},
        bySource: {},
        recent24h: 0,
        recent7days: 0,
        withCVE: 0
      };
    }

    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const calculatedStats = {
      total: threats.length,
      bySeverity: {
        'حرج': 0,
        'عالي': 0,
        'متوسط': 0,
        'منخفض': 0,
        'غير محدد': 0
      },
      byCategory: {},
      bySource: {},
      recent24h: 0,
      recent7days: 0,
      withCVE: 0
    };

    threats.forEach(threat => {
      // إحصائيات الخطورة
      if (calculatedStats.bySeverity.hasOwnProperty(threat.severity)) {
        calculatedStats.bySeverity[threat.severity]++;
      }

      // إحصائيات الفئة
      calculatedStats.byCategory[threat.category] = 
        (calculatedStats.byCategory[threat.category] || 0) + 1;

      // إحصائيات المصدر
      calculatedStats.bySource[threat.source] = 
        (calculatedStats.bySource[threat.source] || 0) + 1;

      // التهديدات الحديثة
      const threatTime = new Date(threat.time);
      if (threatTime >= oneDayAgo) {
        calculatedStats.recent24h++;
      }
      if (threatTime >= sevenDaysAgo) {
        calculatedStats.recent7days++;
      }

      // التهديدات مع CVE
      if (threat.cve) {
        calculatedStats.withCVE++;
      }
    });

    return calculatedStats;
  }, [threats]);

  return stats;
};

/**
 * Hook لحساب النسب المئوية للإحصائيات
 */
export const useStatsPercentages = (stats) => {
  const percentages = useMemo(() => {
    if (!stats || stats.total === 0) {
      return {
        severityPercentages: {},
        categoryPercentages: {},
        sourcePercentages: {},
        cvePercentage: 0,
        recent24hPercentage: 0
      };
    }

    const severityPercentages = {};
    Object.entries(stats.bySeverity || {}).forEach(([severity, count]) => {
      severityPercentages[severity] = ((count / stats.total) * 100).toFixed(1);
    });

    const categoryPercentages = {};
    Object.entries(stats.byCategory || {}).forEach(([category, count]) => {
      categoryPercentages[category] = ((count / stats.total) * 100).toFixed(1);
    });

    const sourcePercentages = {};
    Object.entries(stats.bySource || {}).forEach(([source, count]) => {
      sourcePercentages[source] = ((count / stats.total) * 100).toFixed(1);
    });

    return {
      severityPercentages,
      categoryPercentages,
      sourcePercentages,
      cvePercentage: ((stats.withCVE / stats.total) * 100).toFixed(1),
      recent24hPercentage: ((stats.recent24h / stats.total) * 100).toFixed(1)
    };
  }, [stats]);

  return percentages;
};

/**
 * Hook لتنسيق الإحصائيات للعرض
 */
export const useFormattedStats = (stats) => {
  const formatted = useMemo(() => {
    if (!stats) return null;

    return {
      // إحصائيات الخطورة مع الألوان
      severityStats: [
        {
          label: 'حرج',
          value: stats.bySeverity?.['حرج'] || 0,
          color: 'text-red-500',
          bgColor: 'bg-red-500/10',
          borderColor: 'border-red-500'
        },
        {
          label: 'عالي',
          value: stats.bySeverity?.['عالي'] || 0,
          color: 'text-orange-500',
          bgColor: 'bg-orange-500/10',
          borderColor: 'border-orange-500'
        },
        {
          label: 'متوسط',
          value: stats.bySeverity?.['متوسط'] || 0,
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-500/10',
          borderColor: 'border-yellow-500'
        },
        {
          label: 'منخفض',
          value: stats.bySeverity?.['منخفض'] || 0,
          color: 'text-green-500',
          bgColor: 'bg-green-500/10',
          borderColor: 'border-green-500'
        }
      ],

      // أهم الفئات
      topCategories: Object.entries(stats.byCategory || {})
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([category, count]) => ({ category, count })),

      // أهم المصادر
      topSources: Object.entries(stats.bySource || {})
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([source, count]) => ({ source, count })),

      // إحصائيات سريعة
      quickStats: [
        {
          label: 'إجمالي التهديدات',
          value: stats.total || 0,
          icon: '🛡️'
        },
        {
          label: 'آخر 24 ساعة',
          value: stats.recent24h || 0,
          icon: '🕐'
        },
        {
          label: 'آخر 7 أيام',
          value: stats.recent7days || 0,
          icon: '📅'
        },
        {
          label: 'مع CVE',
          value: stats.withCVE || 0,
          icon: '🔍'
        }
      ]
    };
  }, [stats]);

  return formatted;
};


