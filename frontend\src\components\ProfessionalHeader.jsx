/**
 * رأس احترافي جديد بالكامل
 */

import React, { useState, useEffect } from 'react';

const ProfessionalHeader = ({ activeTab, setActiveTab, tabs = [] }) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [notifications] = useState(5);

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      clearInterval(timer);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const formatTime = (date) => {
    return new Intl.DateTimeFormat('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).format(date);
  };

  const formatDate = (date) => {
    return new Intl.DateTimeFormat('ar-SA', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date);
  };

  return (
    <header className="bg-card-elevated sticky top-0 z-50 border-b border-light backdrop-filter backdrop-blur-xl">
      <div className="container mx-auto">
        {/* الصف العلوي */}
        <div className="flex items-center justify-between py-4">
          {/* الشعار والعنوان */}
          <div className="flex items-center gap-4">
            <div className="relative group">
              <div className="w-14 h-14 bg-gradient-primary rounded-2xl flex items-center justify-center shadow-xl hover:scale-105 transition-all duration-300 cursor-pointer">
                <span className="text-3xl filter drop-shadow-lg">🛡️</span>
              </div>
              {notifications > 0 && (
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-error rounded-full flex items-center justify-center animate-pulse">
                  <span className="text-xs text-white font-bold">{notifications}</span>
                </div>
              )}
            </div>
            
            <div className="space-y-1">
              <h1 className="text-3xl font-extrabold text-primary leading-tight">
                <span className="bg-gradient-primary bg-clip-text text-transparent">
                  مراقب التهديدات السيبرانية
                </span>
              </h1>
              <div className="flex items-center gap-3 text-sm">
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${isOnline ? 'bg-success animate-pulse' : 'bg-error'}`}></div>
                  <span className={`font-medium ${isOnline ? 'text-success' : 'text-error'}`}>
                    {isOnline ? 'متصل' : 'غير متصل'}
                  </span>
                </div>
                <span className="text-muted">•</span>
                <span className="text-muted">نظام مراقبة متقدم</span>
                <span className="bg-info/20 text-info px-2 py-1 rounded-full text-xs font-medium">
                  الإصدار 3.0
                </span>
              </div>
            </div>
          </div>

          {/* معلومات الوقت والحالة */}
          <div className="flex items-center gap-4">
            {/* التاريخ والوقت */}
            <div className="hidden lg:block text-right">
              <div className="text-xs text-muted mb-1">{formatDate(currentTime)}</div>
              <div className="text-2xl font-bold text-primary font-mono tracking-wider">
                {formatTime(currentTime)}
              </div>
            </div>

            {/* أزرار التحكم */}
            <div className="flex items-center gap-2">
              <ActionButton 
                icon="🔄" 
                tooltip="تحديث البيانات"
                onClick={() => window.location.reload()}
                className="hover:rotate-180"
              />
              <ActionButton 
                icon="🔔" 
                tooltip="الإشعارات"
                badge={notifications}
                className="hover:scale-110"
              />
              <ActionButton 
                icon="⚙️" 
                tooltip="الإعدادات"
                className="hover:rotate-90"
              />
              <ActionButton 
                icon="👤" 
                tooltip="الملف الشخصي"
                className="hover:scale-110"
              />
            </div>
          </div>
        </div>

        {/* شريط التنقل */}
        <nav className="border-t border-light/50">
          <div className="flex items-center justify-between py-3">
            {/* التبويبات الرئيسية */}
            <div className="flex items-center gap-1">
              {tabs.map((tab) => (
                <NavTab
                  key={tab.id}
                  tab={tab}
                  isActive={activeTab === tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  badge={tab.id === 'alerts' ? notifications : null}
                />
              ))}
            </div>

            {/* مؤشرات سريعة */}
            <div className="hidden md:flex items-center gap-4 text-sm">
              <StatusIndicator 
                label="التهديدات النشطة" 
                value="23" 
                color="error" 
              />
              <StatusIndicator 
                label="المصادر المتصلة" 
                value="6/6" 
                color="success" 
              />
              <StatusIndicator 
                label="آخر تحديث" 
                value="منذ دقيقتين" 
                color="info" 
              />
            </div>
          </div>
        </nav>

        {/* شريط التقدم */}
        <div className="h-1 bg-surface/30 overflow-hidden">
          <div 
            className="h-full bg-gradient-primary transition-all duration-1000 ease-out"
            style={{ width: '75%' }}
          ></div>
        </div>
      </div>
    </header>
  );
};

/**
 * مكون زر الإجراء
 */
const ActionButton = ({ icon, tooltip, badge, onClick, className = '' }) => {
  return (
    <button
      onClick={onClick}
      title={tooltip}
      className={`
        relative w-10 h-10 bg-surface/50 hover:bg-surface rounded-xl 
        flex items-center justify-center transition-all duration-300
        hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50
        ${className}
      `}
    >
      <span className="text-lg transition-transform duration-300">{icon}</span>
      
      {badge && (
        <div className="absolute -top-1 -right-1 w-5 h-5 bg-error rounded-full flex items-center justify-center animate-bounce">
          <span className="text-xs text-white font-bold">{badge}</span>
        </div>
      )}
    </button>
  );
};

/**
 * مكون تبويب التنقل
 */
const NavTab = ({ tab, isActive, onClick, badge }) => {
  return (
    <button
      onClick={onClick}
      className={`
        relative flex items-center gap-3 px-6 py-3 rounded-xl font-medium
        transition-all duration-300 group
        ${isActive 
          ? 'bg-gradient-primary text-white shadow-lg scale-105' 
          : 'text-muted hover:text-secondary hover:bg-surface/50 hover:scale-105'
        }
      `}
    >
      <span className="text-xl group-hover:scale-110 transition-transform duration-300">
        {tab.icon}
      </span>
      <span className="hidden sm:block">{tab.name}</span>
      
      {badge && (
        <div className="absolute -top-1 -right-1 w-5 h-5 bg-error rounded-full flex items-center justify-center animate-pulse">
          <span className="text-xs text-white font-bold">{badge}</span>
        </div>
      )}
    </button>
  );
};

/**
 * مكون مؤشر الحالة
 */
const StatusIndicator = ({ label, value, color }) => {
  const colorClasses = {
    success: 'text-success',
    error: 'text-error',
    warning: 'text-warning',
    info: 'text-info'
  };

  return (
    <div className="flex items-center gap-2">
      <span className="text-muted">{label}:</span>
      <span className={`font-semibold ${colorClasses[color]}`}>{value}</span>
    </div>
  );
};

export default ProfessionalHeader;
